plugins {
    alias(libs.plugins.androidApplication)
}

android {
    namespace 'com.example.myapplication'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.myapplication"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    implementation 'net.gotev:uploadservice:4.9.0'
    implementation 'net.gotev:uploadservice-okhttp:4.9.0'
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation 'androidx.core:core:1.12.0'
    implementation "androidx.work:work-runtime:2.8.1"
    implementation "androidx.lifecycle:lifecycle-viewmodel:2.6.1"
    implementation "androidx.lifecycle:lifecycle-livedata:2.6.1"
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.arthenica:ffmpeg-kit-min-gpl:6.0-2.LTS'

}