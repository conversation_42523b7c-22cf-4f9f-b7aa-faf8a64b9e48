package com.example.myapplication;

import java.util.List;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface ApiService {
    @GET(Config.API_GET_TASKS)
    Call<List<Task>> getTaskList();

    @POST(Config.API_CREATE_TASK) // New endpoint
    Call<Task> createTask(@Body Task task);

    @GET(Config.API_GET_VIDEOS)
    Call<List<Video>> getVideoList(@Query("taskId") long taskId, @Query("status") String status);

    @POST(Config.API_CREATE_VIDEO)
    Call<Video> createVideo(@Body Video video);

    @POST(Config.API_UPDATE_STATUS)
    Call<Void> updateVideoStatus(@Body Video video);

    @PUT(Config.API_UPDATE_VIDEO_STATUS)
    Call<Void> updateVideoStatus(@Path("id") String id, @Body String status);

    @PUT(Config.API_UPDATE_VIDEO_PROGRESS)
    Call<Void> updateVideoProgress(@Path("id") String id, @Body int progress);

    @PUT(Config.API_UPDATE_VIDEO)
    Call<Void> updateVideo(@Path("id") String id, @Body Video video);

    @DELETE(Config.API_DELETE_VIDEO)
    Call<Void> deleteVideo(@Path("id") String id);
    
    @GET(Config.API_GET_VIDEOS_BY_USER)
    Call<List<Video>> getVideosByUserId(@Query("userId") String userId);
}