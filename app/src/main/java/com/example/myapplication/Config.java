package com.example.myapplication;

public class Config {
    // 服务器基础URL
    public static final String BASE_URL = "http://192.168.31.132:8080/";
//    public static final String BASE_URL = "http://218.56.149.18:9653/";
    // API端点
    public static final String API_GET_TASKS = "/upload/tasks";
    public static final String API_CREATE_TASK = "/upload/tasks";
    public static final String API_GET_VIDEOS = "/upload/videos";
    public static final String API_CREATE_VIDEO = "/upload/videos";
    public static final String API_UPDATE_STATUS = "/upload/updateStatus";
    public static final String API_UPDATE_VIDEO_STATUS = "videos/{id}/status";
    public static final String API_UPDATE_VIDEO_PROGRESS = "/upload/videos/{id}/progress";
    public static final String API_UPDATE_VIDEO = "videos/{id}";
    public static final String API_DELETE_VIDEO = "/upload/videos/{id}";
    public static final String API_GET_VIDEOS_BY_USER = "/upload/videos/by-user";
    public static final String API_UPLOAD_FILE = "/upload/file";
    public static final String API_UPLOAD_STATUS = "/upload/status";
}