package com.example.myapplication;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {
    private RecyclerView recyclerViewTasks;
    private TaskAdapter taskAdapter;
    private ApiService apiService;
    private static final String TAG = "MainActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_FOREGROUND);
        setContentView(R.layout.activity_main);

        recyclerViewTasks = findViewById(R.id.recycler_view_tasks);
        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(this));

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(Config.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        apiService = retrofit.create(ApiService.class);
    }

    private void handleDeepLink(Intent intent) {
        Uri data = intent.getData();
        if (data != null && "myvideo".equals(data.getScheme())) {
            String host = data.getHost();
            String sampleCode = data.getQueryParameter("sampleCode");
            String userId = data.getQueryParameter("userId");
            Log.d(TAG, "handleDeepLink: host=" + host + ", sampleCode=" + sampleCode + ", userId=" + userId);

            if (userId == null || userId.trim().isEmpty()) {
                Log.e(TAG, "未传入用户ID参数，关闭应用");
                Toast.makeText(this, "未传入用户ID参数，禁止打开", Toast.LENGTH_SHORT).show();
                finish();
                return;
            }

            if (sampleCode != null) {
                try {
                    long taskId = Long.parseLong(sampleCode);
                    if ("video".equals(host)) {
                        checkAndCreateTaskForVideo(taskId, userId);
                    } else if ("list".equals(host)) {
                        checkAndCreateTask(taskId, userId);
                    } else {
                        Log.w(TAG, "未知的 deep link 类型: " + host);
                        Toast.makeText(this, "未知的 deep link 类型: " + host, Toast.LENGTH_SHORT).show();
                    }
                } catch (NumberFormatException e) {
                    Log.e(TAG, "Invalid sampleCode format: " + sampleCode, e);
                    Toast.makeText(this, "Invalid sampleCode format", Toast.LENGTH_SHORT).show();
                }
            } else {
                // Only userId provided, open UserVideoListActivity
                Intent userVideoIntent = new Intent(MainActivity.this, UserVideoListActivity.class);
                userVideoIntent.putExtra("userId", userId);
                Log.d(TAG, "启动 UserVideoListActivity: userId=" + userId);
                startActivity(userVideoIntent);
            }
        } else {
            Log.e(TAG, "未传入有效 deep link，关闭应用");
            Toast.makeText(this, "未传入有效 deep link，禁止打开", Toast.LENGTH_SHORT).show();
            finish();
        }
    }

    private void checkAndCreateTaskForVideo(long taskId, String userId) {
        // 检查任务是否存在
        apiService.getTaskList().enqueue(new Callback<List<Task>>() {
            @Override
            public void onResponse(Call<List<Task>> call, Response<List<Task>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Task> tasks = response.body();
                    boolean taskExists = false;
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                        taskExists = tasks.stream().anyMatch(t -> t.getId() == taskId);
                    }

                    if (taskExists) {
                        Log.d(TAG, "任务已存在，taskId=" + taskId);
                        openVideoListAndRecord(taskId, userId);
                    } else {
                        createNewTaskForVideo(taskId, userId);
                    }
                } else {
                    Log.e(TAG, "检查任务失败，HTTP 状态码: " + response.code());
                    Toast.makeText(MainActivity.this, "检查任务失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Task>> call, Throwable t) {
                Log.e(TAG, "检查任务失败，异常: " + t.getMessage(), t);
                Toast.makeText(MainActivity.this, "检查任务失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void createNewTaskForVideo(long taskId, String userId) {
        Task newTask = new Task(taskId, userId); // 使用 userId 作为任务名称
        apiService.createTask(newTask).enqueue(new Callback<Task>() {
            @Override
            public void onResponse(Call<Task> call, Response<Task> response) {
                if (response.isSuccessful()) {
                    Log.d(TAG, "任务创建成功，taskId=" + taskId);
//                    Toast.makeText(MainActivity.this, "任务创建成功", Toast.LENGTH_SHORT).show();
                    openVideoListAndRecord(taskId, userId);
                } else {
                    Log.e(TAG, "任务创建失败，HTTP 状态码: " + response.code());
//                    Toast.makeText(MainActivity.this, "任务创建失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<Task> call, Throwable t) {
                Log.e(TAG, "创建任务失败，异常: " + t.getMessage(), t);
//                Toast.makeText(MainActivity.this, "创建任务失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void openVideoListAndRecord(long taskId, String userId) {
        Intent intent = new Intent(MainActivity.this, VideoListActivity.class);
        intent.putExtra("taskId", taskId);
        intent.putExtra("userId", userId);
        intent.putExtra("autoRecord", true); // 添加标志，指示自动录像
        Log.d(TAG, "启动 VideoListActivity (autoRecord): taskId=" + taskId + ", userId=" + userId);
        startActivity(intent);
    }

    private void checkAndCreateTask(long taskId, String userId) {
        // 检查任务是否存在
        apiService.getTaskList().enqueue(new Callback<List<Task>>() {
            @Override
            public void onResponse(Call<List<Task>> call, Response<List<Task>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Task> tasks = response.body();
                    boolean taskExists = false;
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                        taskExists = tasks.stream().anyMatch(t -> t.getId() == taskId);
                    }

                    if (taskExists) {
                        Log.d(TAG, "任务已存在，taskId=" + taskId);
                        openVideoList(taskId, userId);
                    } else {
                        createNewTask(taskId, userId);
                    }
                } else {
                    Log.e(TAG, "检查任务失败，HTTP 状态码: " + response.code());
                    Toast.makeText(MainActivity.this, "检查任务失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<Task>> call, Throwable t) {
                Log.e(TAG, "检查任务失败，异常: " + t.getMessage(), t);
                Toast.makeText(MainActivity.this, "检查任务失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void createNewTask(long taskId, String userId) {
        Task newTask = new Task(taskId, userId); // 使用 userId 作为任务名称
        apiService.createTask(newTask).enqueue(new Callback<Task>() {
            @Override
            public void onResponse(Call<Task> call, Response<Task> response) {
                if (response.isSuccessful()) {
                    Log.d(TAG, "任务创建成功，taskId=" + taskId);
//                    Toast.makeText(MainActivity.this, "任务创建成功", Toast.LENGTH_SHORT).show();
                    openVideoList(taskId, userId);
                } else {
                    Log.e(TAG, "任务创建失败，HTTP 状态码: " + response.code());
//                    Toast.makeText(MainActivity.this, "任务创建失败", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<Task> call, Throwable t) {
                Log.e(TAG, "创建任务失败，异常: " + t.getMessage(), t);
//                Toast.makeText(MainActivity.this, "创建任务失败: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void openVideoList(long taskId, String userId) {
        Intent intent = new Intent(MainActivity.this, VideoListActivity.class);
        intent.putExtra("taskId", taskId);
        intent.putExtra("userId", userId);
        Log.d(TAG, "启动 VideoListActivity: taskId=" + taskId + ", userId=" + userId);
        startActivity(intent);
    }

    private void fetchTaskList() {
        apiService.getTaskList().enqueue(new Callback<List<Task>>() {
            @Override
            public void onResponse(Call<List<Task>> call, Response<List<Task>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    List<Task> taskList = response.body();
                    taskAdapter = new TaskAdapter(taskList, task -> {
                        Intent intent = new Intent(MainActivity.this, VideoListActivity.class);
                        intent.putExtra("taskId", task.getId());
                        // 注意：此处未传递 userId，可能需要从 Task 或其他来源获取
                        Log.d(TAG, "从任务列表启动 VideoListActivity: taskId=" + task.getId());
                        startActivity(intent);
                    });
                    recyclerViewTasks.setAdapter(taskAdapter);
                } else {
                    Log.e(TAG, "获取任务列表失败，HTTP 状态码: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<List<Task>> call, Throwable t) {
                Log.e(TAG, "获取任务列表失败，异常: " + t.getMessage(), t);
                Toast.makeText(MainActivity.this, "获取任务列表失败", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        handleDeepLink(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        handleDeepLink(getIntent());
        fetchTaskList();
    }
}