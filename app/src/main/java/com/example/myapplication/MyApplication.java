package com.example.myapplication;

import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;

import net.gotev.uploadservice.BuildConfig;
import net.gotev.uploadservice.UploadServiceConfig;

public class MyApplication extends Application {

    private static final String CHANNEL_ID = "upload_channel";

    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化 UploadService，指定广播接收器标志
        UploadServiceConfig.initialize(
                this,           // context
                CHANNEL_ID,    // defaultNotificationChannel
                BuildConfig.DEBUG

        );

        // 创建通知渠道（Android 8+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Upload Channel",
                    NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription("Channel for upload notifications");
            channel.enableLights(true);
            channel.enableVibration(true);
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }
}