package com.example.myapplication;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 录制会话持久化管理器
 * 用于在 Activity 退出/后台时保存录制进度，回到 App 时可继续录制。
 */
public class RecordingSessionManager {

    private static final String PREF = "record_session_prefs";

    private static final String KEY_ACTIVE = "active";
    private static final String KEY_SEGMENTS = "segments";         // 以 | 分隔的绝对路径列表
    private static final String KEY_TOTAL_MS = "total_ms";         // 已录制总时长（ms）
    private static final String KEY_SEG_COUNT = "segment_count";   // 已完成片段数
    private static final String KEY_FINAL_PATH = "final_path";     // 最终输出路径（占位）
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_TASK_ID = "task_id";

    public static class Session {
        public boolean active;
        public List<String> segments = new ArrayList<>();
        public long totalMs;
        public int segmentCount;
        public String finalPath;
        public String userId;
        public long taskId;
    }

    public static void save(Context ctx, Session s) {
        SharedPreferences sp = ctx.getSharedPreferences(PREF, Context.MODE_PRIVATE);
        String joined = String.join("|", s.segments);
        sp.edit()
                .putBoolean(KEY_ACTIVE, s.active)
                .putString(KEY_SEGMENTS, joined)
                .putLong(KEY_TOTAL_MS, s.totalMs)
                .putInt(KEY_SEG_COUNT, s.segmentCount)
                .putString(KEY_FINAL_PATH, s.finalPath)
                .putString(KEY_USER_ID, s.userId)
                .putLong(KEY_TASK_ID, s.taskId)
                .apply();
    }

    public static Session load(Context ctx) {
        SharedPreferences sp = ctx.getSharedPreferences(PREF, Context.MODE_PRIVATE);
        Session s = new Session();
        s.active = sp.getBoolean(KEY_ACTIVE, false);
        String seg = sp.getString(KEY_SEGMENTS, "");
        if (!seg.isEmpty()) {
            s.segments = new ArrayList<>(Arrays.asList(seg.split("\\|")));
        }
        s.totalMs = sp.getLong(KEY_TOTAL_MS, 0L);
        s.segmentCount = sp.getInt(KEY_SEG_COUNT, 0);
        s.finalPath = sp.getString(KEY_FINAL_PATH, null);
        s.userId = sp.getString(KEY_USER_ID, null);
        s.taskId = sp.getLong(KEY_TASK_ID, 0L);
        return s;
    }

    public static boolean hasActive(Context ctx) {
        SharedPreferences sp = ctx.getSharedPreferences(PREF, Context.MODE_PRIVATE);
        return sp.getBoolean(KEY_ACTIVE, false);
    }

    public static void clear(Context ctx) {
        ctx.getSharedPreferences(PREF, Context.MODE_PRIVATE).edit().clear().apply();
    }
}

