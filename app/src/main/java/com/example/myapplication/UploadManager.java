package com.example.myapplication;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import okio.BufferedSink;
import okio.ForwardingSink;
import okio.Okio;

public class UploadManager {
    private static final int MAX_CONCURRENT_UPLOADS = 4;
    private static final int CHUNK_SIZE = 1024 * 1024 * 5; // 5MB
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    private static UploadManager instance;
    private final Semaphore uploadSemaphore = new Semaphore(MAX_CONCURRENT_UPLOADS);
    private final Map<String, List<Call>> uploadCalls = new HashMap<>();
    private final Map<String, Integer> retryCounts = new HashMap<>();
    private final Map<String, Boolean> pausedUploads = new HashMap<>();
    private final Context context;
    
    private UploadManager(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized UploadManager getInstance(Context context) {
        if (instance == null) {
            instance = new UploadManager(context);
        }
        return instance;
    }
    
    public interface UploadProgressListener {
        void onProgressUpdated(int progress);
        void onUploadCompleted();
        void onUploadFailed(String errorMessage);
    }
    
    public void uploadChunks(Video video, VideoAdapter.VideoViewHolder holder, int position, File file, 
                           int totalChunks, String uploadId, List<Integer> uploadedChunks, 
                           UploadProgressListener listener) {
        // 启动前台服务开始上传
        UploadService.startUploadService(context, video.getFilePath(), video.getTaskId());
        
        // 检查视频是否已暂停
        if (isUploadPaused(video.getFilePath())) {
            Log.d("UploadManager", "Upload is paused for: " + video.getFilePath());
            return;
        }
        
        AtomicInteger completedChunks = new AtomicInteger(uploadedChunks.size());
        List<Call> calls = new ArrayList<>();
        AtomicInteger failedChunks = new AtomicInteger(0);

        Log.d("UploadManager", "Starting uploadChunks, available permits: " + uploadSemaphore.availablePermits());
        for (int i = 0; i < totalChunks; i++) {
            // 如果分片已经上传，跳过
            if (uploadedChunks.contains(i)) {
                completedChunks.incrementAndGet();
                continue;
            }
            
            // 检查视频是否已暂停
            if (isUploadPaused(video.getFilePath())) {
                Log.d("UploadManager", "Upload paused, stopping at chunk " + i);
                releaseAllPermits();
                return;
            }

            final int chunk = i;
            try {
                uploadSemaphore.acquire();
                
                // 再次检查是否已暂停
                if (isUploadPaused(video.getFilePath())) {
                    Log.d("UploadManager", "Upload paused after acquiring semaphore, chunk " + chunk);
                    uploadSemaphore.release();
                    releaseAllPermits();
                    return;
                }
                
                Call call = uploadChunkWithOkHttp(video, holder, position, file, chunk, totalChunks, uploadId, 
                    new OnUploadCompleteListener() {
                        @Override
                        public void onComplete() {
                            // 检查是否已暂停
                            if (isUploadPaused(video.getFilePath())) {
                                Log.d("UploadManager", "Upload was paused during chunk completion, chunk " + chunk);
                                uploadSemaphore.release();
                                return;
                            }
                            
                            int completed = completedChunks.incrementAndGet();
                            int progress = (int) (completed * 100 / totalChunks);
                            
                            // 通知进度更新
                            listener.onProgressUpdated(progress);
                            
                            // 检查是否所有分片都已完成
                            if (completed == totalChunks && !isUploadPaused(video.getFilePath())) {
                                // 上传完成
                                listener.onUploadCompleted();
                                // 停止前台服务
                                UploadService.pauseUploadService(context, video.getFilePath());
                            }
                            
                            uploadSemaphore.release();
                        }
                    });
                    
                if (call != null) {
                    calls.add(call);
                } else {
                    uploadSemaphore.release();
                }
            } catch (InterruptedException e) {
                Log.e("UploadManager", "Semaphore acquire interrupted for chunk " + chunk + ": " + e.getMessage());
                uploadSemaphore.release();
                Thread.currentThread().interrupt();
            }
        }
        uploadCalls.put(video.getFilePath(), calls);
    }
    
    private void releaseAllPermits() {
        while (uploadSemaphore.availablePermits() < MAX_CONCURRENT_UPLOADS) {
            uploadSemaphore.release();
        }
    }
    
    private Call uploadChunkWithOkHttp(Video video, VideoAdapter.VideoViewHolder holder, int position, File file, 
                                     int chunk, int totalChunks, String uploadId, 
                                     OnUploadCompleteListener completeListener) {
        try {
            // 检查是否已暂停
            if (isUploadPaused(video.getFilePath())) {
                Log.d("UploadManager", "Skipping chunk " + chunk + " due to paused state");
                return null;
            }
            
            long startOffset = (long) chunk * CHUNK_SIZE;
            long remainingBytes = file.length() - startOffset;
            int bytesToUpload = (int) Math.min(CHUNK_SIZE, remainingBytes);

            RequestBody requestBody = new RequestBody() {
                @Override
                public MediaType contentType() {
                    return MediaType.parse("video/mp4");
                }

                @Override
                public long contentLength() {
                    return bytesToUpload;
                }

                @Override
                public void writeTo(BufferedSink sink) {
                    try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r")) {
                        randomAccessFile.seek(startOffset);
                        byte[] buffer = new byte[8192];
                        long bytesRemaining = bytesToUpload;
                        while (bytesRemaining > 0) {
                            int bytesRead = randomAccessFile.read(buffer, 0, (int) Math.min(buffer.length, bytesRemaining));
                            if (bytesRead == -1) break;
                            sink.write(buffer, 0, bytesRead);
                            bytesRemaining -= bytesRead;
                        }
                    } catch (IOException e) {
                        Log.e("UploadManager", "Error reading file for chunk " + chunk + ": " + e.getMessage());
                    }
                }
            };

            // 包装请求体以跟踪上传进度
            ProgressRequestBody progressRequestBody = new ProgressRequestBody(requestBody, new ProgressRequestBody.ProgressListener() {
                @Override
                public void onProgress(long bytesWritten, long contentLength) {
                    // 这里可以处理进度更新
                }
            });

            OkHttpClient client = new OkHttpClient.Builder()
                    .retryOnConnectionFailure(true)
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(300, TimeUnit.SECONDS)
                    .writeTimeout(120, TimeUnit.SECONDS)
                    .build();

            MultipartBody multipartBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", file.getName(), progressRequestBody)
                    .addFormDataPart("status", video.getStatus())
                    .addFormDataPart("id", video.getId())
                    .addFormDataPart("taskId", String.valueOf(video.getTaskId()))
                    .addFormDataPart("chunk", String.valueOf(chunk))
                    .addFormDataPart("totalChunks", String.valueOf(totalChunks))
                    .addFormDataPart("uploadId", uploadId)
                    .build();

            Request request = new Request.Builder()
                    .url(Config.BASE_URL + "upload/file")
                    .post(multipartBody)
                    .build();

            Call call = client.newCall(request);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    if (call.isCanceled()) {
                        Log.d("UploadManager", "Chunk " + chunk + " canceled due to pause");
                    } else {
                        Log.e("UploadManager", "Chunk " + chunk + " failed: " + e.getMessage());
                        uploadSemaphore.release();
                        // 可以在这里添加重试逻辑
                    }
                }

                @Override
                public void onResponse(Call call, Response response) {
                    try {
                        String responseBody = response.body().string();
                        if (response.isSuccessful()) {
                            Log.d("UploadManager", "Chunk " + chunk + " uploaded successfully, response: " + responseBody);
                            if (completeListener != null && !isUploadPaused(video.getFilePath())) {
                                completeListener.onComplete();
                            }
                        } else {
                            Log.e("UploadManager", "Chunk " + chunk + " upload failed: " + response.message());
                            uploadSemaphore.release();
                            // 可以在这里添加重试逻辑
                        }
                    } catch (IOException e) {
                        Log.e("UploadManager", "Error reading response for chunk " + chunk + ": " + e.getMessage());
                    } finally {
                        if (response.body() != null) {
                            response.body().close();
                        }
                        response.close();
                    }
                }
            });
            return call;
        } catch (Exception e) {
            Log.e("UploadManager", "Unexpected error uploading chunk " + chunk + ": " + e.getMessage());
            uploadSemaphore.release();
            return null;
        }
    }
    
    public void pauseUpload(String videoPath) {
        // 标记上传为暂停状态
        pausedUploads.put(videoPath, true);
        
        // 取消所有正在进行的请求
        List<Call> calls = uploadCalls.get(videoPath);
        if (calls != null) {
            for (Call call : calls) {
                if (!call.isCanceled()) {
                    call.cancel();
                }
            }
            calls.clear();
        }
        
        // 清理重试计数
        retryCounts.remove(videoPath);
        
        // 释放所有信号量
        releaseAllPermits();
        
        // 通知服务暂停上传
        UploadService.pauseUploadService(context, videoPath);
        
        Log.d("UploadManager", "Paused upload for: " + videoPath);
    }
    
    public void pauseAllUploads(List<Video> videoList) {
        for (Video video : videoList) {
            if ("上传中".equals(video.getStatus())) {
                pauseUpload(video.getFilePath());
            }
        }
    }
    
    public void resumeUpload(String videoPath) {
        // 清除指定视频的暂停状态
        pausedUploads.remove(videoPath);
    }
    
    public void resumeAllUploads(List<Video> videoList) {
        // 清除所有视频的暂停状态，确保从后台返回时能恢复上传
        pausedUploads.clear();
    }
    
    private boolean isUploadPaused(String videoPath) {
        Boolean paused = pausedUploads.get(videoPath);
        return paused != null && paused;
    }
    
    interface OnUploadCompleteListener {
        void onComplete();
    }
    
    public static class ProgressRequestBody extends RequestBody {
        private final RequestBody delegate;
        private final ProgressListener listener;

        public ProgressRequestBody(RequestBody delegate, ProgressListener listener) {
            this.delegate = delegate;
            this.listener = listener;
        }

        @Override
        public MediaType contentType() {
            return delegate.contentType();
        }

        @Override
        public long contentLength() {
            try {
                return delegate.contentLength();
            } catch (IOException e) {
                return -1;
            }
        }

        @Override
        public void writeTo(BufferedSink sink) throws IOException {
            BufferedSink bufferedSink = Okio.buffer(new ForwardingSink(sink) {
                private long bytesWritten = 0L;

                @Override
                public void write(Buffer source, long byteCount) throws IOException {
                    super.write(source, byteCount);
                    bytesWritten += byteCount;
                    if (listener != null) {
                        listener.onProgress(bytesWritten, contentLength());
                    }
                }
            });
            delegate.writeTo(bufferedSink);
            bufferedSink.flush();
        }

        public interface ProgressListener {
            void onProgress(long bytesWritten, long contentLength);
        }
    }
}