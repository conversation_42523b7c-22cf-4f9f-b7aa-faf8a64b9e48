package com.example.myapplication;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class UploadService extends Service {
    private static final String TAG = "UploadService";
    private static final String CHANNEL_ID = "UploadServiceChannel";
    private static final int NOTIFICATION_ID = 1;
    private static final String ACTION_START_UPLOAD = "com.example.myapplication.action.START_UPLOAD";
    private static final String ACTION_PAUSE_UPLOAD = "com.example.myapplication.action.PAUSE_UPLOAD";
    
    private NotificationManager notificationManager;
    private ConcurrentMap<String, Boolean> activeUploads = new ConcurrentHashMap<>();
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "UploadService created");
        createNotificationChannel();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            if (ACTION_START_UPLOAD.equals(action)) {
                String videoPath = intent.getStringExtra("videoPath");
                long taskId = intent.getLongExtra("taskId", -1);
                if (videoPath != null && taskId != -1) {
                    startUpload(videoPath, taskId);
                }
            } else if (ACTION_PAUSE_UPLOAD.equals(action)) {
                String videoPath = intent.getStringExtra("videoPath");
                if (videoPath != null) {
                    pauseUpload(videoPath);
                }
            }
        }
        
        // 如果服务被系统终止，重新启动服务并重传intent
        return START_REDELIVER_INTENT;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 不支持绑定
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "视频上传服务",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("用于在后台上传视频文件");
            
            notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        } else {
            notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        }
    }
    
    private void startForegroundService() {
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("视频上传")
                .setContentText("正在后台上传视频...")
                .setSmallIcon(android.R.drawable.ic_menu_upload)
                .setOngoing(true)
                .build();
        
        startForeground(NOTIFICATION_ID, notification);
    }
    
    private void updateNotification(String videoName, int progress) {
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("正在上传: " + videoName)
                .setContentText("进度: " + progress + "%")
                .setSmallIcon(android.R.drawable.ic_menu_upload)
                .setProgress(100, progress, false)
                .setOngoing(true)
                .build();
        
        notificationManager.notify(NOTIFICATION_ID, notification);
    }
    
    private void startUpload(String videoPath, long taskId) {
        Log.d(TAG, "Starting upload for video: " + videoPath);
        activeUploads.put(videoPath, true);
        
        // 启动前台服务
        startForegroundService();
        
        // 这里应该调用实际的上传逻辑
        // 可以通过UploadManager来处理具体的上传任务
        // UploadManager.getInstance(this).uploadVideo(videoPath, taskId);
    }
    
    private void pauseUpload(String videoPath) {
        Log.d(TAG, "Pausing upload for video: " + videoPath);
        activeUploads.remove(videoPath);
        
        // 暂停上传逻辑
        // UploadManager.getInstance(this).pauseUpload(videoPath);
        
        // 如果没有活跃的上传任务，停止前台服务
        if (activeUploads.isEmpty()) {
            stopForeground(true);
        }
    }
    
    public static void startUploadService(android.content.Context context, String videoPath, long taskId) {
        Intent intent = new Intent(context, UploadService.class);
        intent.setAction(ACTION_START_UPLOAD);
        intent.putExtra("videoPath", videoPath);
        intent.putExtra("taskId", taskId);
        context.startService(intent);
    }
    
    public static void pauseUploadService(android.content.Context context, String videoPath) {
        Intent intent = new Intent(context, UploadService.class);
        intent.setAction(ACTION_PAUSE_UPLOAD);
        intent.putExtra("videoPath", videoPath);
        context.startService(intent);
    }
}