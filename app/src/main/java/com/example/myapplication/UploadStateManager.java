package com.example.myapplication;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import java.io.File;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 上传状态管理器
 * 负责管理持久化的uploadId，确保应用重启后能恢复上传进度
 */
public class UploadStateManager {
    
    private static final String PREFS_NAME = "upload_state_prefs";
    private static final String TAG = "UploadStateManager";
    
    /**
     * 为文件生成稳定的uploadId
     * 基于文件路径、大小、修改时间生成，确保同一文件始终得到相同的uploadId
     */
    public static String getStableUploadId(Context context, String filePath, long taskId) {
        File file = new File(filePath);
        if (!file.exists()) {
            Log.e(TAG, "文件不存在: " + filePath);
            return null;
        }
        
        // 生成基于文件特征的稳定ID
        String fileInfo = filePath + "_" + file.length() + "_" + file.lastModified() + "_" + taskId;
        String uploadId = generateMD5(fileInfo);
        
        Log.d(TAG, "生成稳定uploadId: " + uploadId + " for " + filePath);
        return uploadId;
    }
    
    /**
     * 检查文件是否有正在进行的上传
     */
    public static boolean hasOngoingUpload(Context context, String filePath, long taskId) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return false;
        
        return prefs.getBoolean("uploading_" + uploadId, false);
    }
    
    /**
     * 标记上传开始
     */
    public static void markUploadStarted(Context context, String filePath, long taskId) {
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return;
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putBoolean("uploading_" + uploadId, true).apply();
        Log.d(TAG, "标记上传开始: " + uploadId);
    }
    
    /**
     * 标记上传完成
     */
    public static void markUploadCompleted(Context context, String filePath, long taskId) {
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return;
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().remove("uploading_" + uploadId).apply();
        Log.d(TAG, "标记上传完成: " + uploadId);
    }
    
    /**
     * 保存上传进度
     */
    public static void saveUploadProgress(Context context, String filePath, long taskId, int progress, int totalChunks) {
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return;
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit()
                .putInt("progress_" + uploadId, progress)
                .putInt("totalChunks_" + uploadId, totalChunks)
                .putLong("lastUpdate_" + uploadId, System.currentTimeMillis())
                .apply();
    }
    
    /**
     * 获取保存的上传进度
     */
    public static int getSavedProgress(Context context, String filePath, long taskId) {
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return 0;
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getInt("progress_" + uploadId, 0);
    }
    
    /**
     * 获取保存的总分片数
     */
    public static int getSavedTotalChunks(Context context, String filePath, long taskId) {
        String uploadId = getStableUploadId(context, filePath, taskId);
        if (uploadId == null) return 0;
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getInt("totalChunks_" + uploadId, 0);
    }
    
    /**
     * 清理过期的上传记录（超过7天）
     */
    public static void cleanupExpiredRecords(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        long currentTime = System.currentTimeMillis();
        long expireTime = 7 * 24 * 60 * 60 * 1000L; // 7天
        
        for (String key : prefs.getAll().keySet()) {
            if (key.startsWith("lastUpdate_")) {
                long lastUpdate = prefs.getLong(key, 0);
                if (currentTime - lastUpdate > expireTime) {
                    String uploadId = key.replace("lastUpdate_", "");
                    editor.remove("uploading_" + uploadId);
                    editor.remove("progress_" + uploadId);
                    editor.remove("totalChunks_" + uploadId);
                    editor.remove(key);
                    Log.d(TAG, "清理过期记录: " + uploadId);
                }
            }
        }
        editor.apply();
    }
    
    /**
     * 生成MD5哈希
     */
    private static String generateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "MD5算法不可用", e);
            return String.valueOf(input.hashCode());
        }
    }
}
