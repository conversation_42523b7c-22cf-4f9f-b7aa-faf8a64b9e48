package com.example.myapplication;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class UserVideoListActivity extends VideoListActivity {

    private String userId;
    private static final String TAG = "UserVideoListActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 从 savedInstanceState 恢复 userId
        if (savedInstanceState != null) {
            userId = savedInstanceState.getString("userId");
            Log.d(TAG, "从 savedInstanceState 恢复 userId=" + userId);
        } else {
            userId = getIntent().getStringExtra("userId");
        }

        if (userId == null || userId.trim().isEmpty()) {
            Log.e(TAG, "未提供 userId，关闭活动");
            finish();
            return;
        }
        // 设置 taskId 为 0，因为我们不按任务区分
        taskId = 0;
        Log.d(TAG, "onCreate: userId=" + userId);

        // 确保 recyclerViewVideos 初始化
        if (recyclerViewVideos == null) {
            recyclerViewVideos = findViewById(R.id.recycler_view_videos);
            if (recyclerViewVideos == null) {
                Log.e(TAG, "recyclerViewVideos 未找到，检查 activity_video_list.xml");
                finish();
                return;
            }
            recyclerViewVideos.setLayoutManager(new LinearLayoutManager(this));
        }

        // 确保 videoAdapter 初始化
        if (videoAdapter == null) {
            videoAdapter = new VideoAdapter(new ArrayList<>(), video -> {
                File videoFile = new File(video.getFilePath());
                Uri videoUri = FileProvider.getUriForFile(
                        UserVideoListActivity.this,
                        "com.example.myapplication.fileprovider",
                        videoFile);
                Intent intent = new Intent(Intent.ACTION_VIEW);
                intent.setDataAndType(videoUri, "video/mp4");
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                startActivity(intent);
            }, apiService, this, taskId);
            recyclerViewVideos.setAdapter(videoAdapter);
            Log.d(TAG, "videoAdapter 初始化完成");
        }

        // 更新标签页标题，显示 userId
        TextView tabPendingUpload = findViewById(R.id.tabPendingUpload);
        TextView tabUploaded = findViewById(R.id.tabUploaded);
        if (tabPendingUpload == null || tabUploaded == null) {
            Log.e(TAG, "标签页 TextView 未找到，检查 activity_video_list.xml");
            finish();
            return;
        }
        tabPendingUpload.setText("用户 " + userId + " 待上传");
        tabUploaded.setText("用户 " + userId + " 已上传");

        // 恢复状态
        if (savedInstanceState != null) {
            currentTab = savedInstanceState.getString("currentTab", "待上传");
            updateTabUI();
        }

        // 确保首次加载数据
        loadVideos(currentTab);
    }

    @Override
    protected void loadVideos(String tab) {
        String statusQuery;
        if (tab.equals("待上传")) {
            statusQuery = "待上传,上传中,上传失败,已暂停";
        } else if (tab.equals("已上传")) {
            statusQuery = "已上传";
        } else {
            Log.w(TAG, "无效的标签页: " + tab);
            return;
        }

        Log.d(TAG, "开始加载视频，userId=" + userId + ", tab=" + tab + ", statusQuery=" + statusQuery);
        apiService.getVideosByUserId(userId).enqueue(new Callback<List<Video>>() {
            @Override
            public void onResponse(Call<List<Video>> call, Response<List<Video>> response) {
                if (response.isSuccessful()) {
                    List<Video> videos = response.body();
                    Log.d(TAG, "加载视频成功，响应数据: " + (videos != null ? videos.size() : "null") + " 条");
                    List<Video> filteredVideos = new ArrayList<>();
                    if (videos != null) {
                        for (Video video : videos) {
                            if (video == null) {
                                Log.w(TAG, "视频对象为 null，跳过");
                                continue;
                            }
                            Log.d(TAG, "视频: filePath=" + video.getFilePath() + ", status=" + video.getStatus());
                            if (video.getStatus() != null && statusQuery.contains(video.getStatus())) {
                                filteredVideos.add(video);
                            }
                        }
                    }
                    Log.d(TAG, "过滤后视频数量: " + filteredVideos.size());
                    if (videoAdapter != null) {
                        videoAdapter.updateVideos(filteredVideos);
                        recyclerViewVideos.post(() -> videoAdapter.notifyDataSetChanged());
                    } else {
                        Log.e(TAG, "videoAdapter 为 null，无法更新视频列表");
                    }
                } else {
                    Log.e(TAG, "加载视频失败，HTTP 状态码: " + response.code() + ", 错误: " + response.message());
                }
            }

            @Override
            public void onFailure(Call<List<Video>> call, Throwable t) {
                Log.e(TAG, "加载视频失败，异常: " + t.getMessage(), t);
            }
        });
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent: intent=" + intent.getData());
        setIntent(intent);

        String newUserId = intent.getStringExtra("userId");
        if (newUserId != null && !newUserId.trim().isEmpty() && !newUserId.equals(userId)) {
            userId = newUserId;
            Log.d(TAG, "onNewIntent: 更新 userId=" + userId);
            // 更新标签页标题
            TextView tabPendingUpload = findViewById(R.id.tabPendingUpload);
            TextView tabUploaded = findViewById(R.id.tabUploaded);
            if (tabPendingUpload != null && tabUploaded != null) {
                tabPendingUpload.setText("用户 " + userId + " 待上传");
                tabUploaded.setText("用户 " + userId + " 已上传");
            }
            loadVideos(currentTab);
        } else {
            Log.d(TAG, "onNewIntent: userId 未更改或无效，忽略");
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString("currentTab", currentTab);
        outState.putString("userId", userId); // 保存 userId
        Log.d(TAG, "onSaveInstanceState: 保存 currentTab=" + currentTab + ", userId=" + userId);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy 被调用");
    }
}