package com.example.myapplication;

import androidx.annotation.NonNull;
import com.google.gson.annotations.SerializedName;
import com.google.gson.annotations.Expose;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class Video {
    private String id;          // 视频ID，与后端保持一致（后端为int，前端用String兼容）
    private String filePath;    // 文件路径
    private String path;        // 在线路径（新增）
    private String status;      // 状态
    private long taskId;         // 关联的任务ID
    private String userId; // Added for userId
    private int progress;       // 上传进度百分比
    private long uploadedBytes; // 已上传字节数
    private int totalChunks;    // 总分片数
    private int currentChunk;   // 当前分片
    private boolean isRemoved;  // 是否已删除
    private boolean isInvalid;  // 是否已作废
    private int ishebing;       // 合并状态：0=未合并，1=合并中，2=合并完成（新增）
    
    @SerializedName("createdAt")
    @Expose
    private Date createdAt; // Added for creation time

    public Video(String filePath, String status, long taskId) {
        this.filePath = filePath;
        this.status = status;
        this.taskId = taskId;
        this.progress = 0;
        this.uploadedBytes = 0;
        this.totalChunks = 0;
        this.currentChunk = 0;
        this.isRemoved = false;
        this.isInvalid = false;
        this.ishebing = 0; // 默认未合并
        this.createdAt = new Date(); // 初始化创建时间
    }

    // Getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    public String getPath() { return path; }
    public void setPath(String path) { this.path = path; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    public long getTaskId() { return taskId; }
    public void setTaskId(int taskId) { this.taskId = taskId; }
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    public int getProgress() { return progress; }
    public void setProgress(int progress) { this.progress = progress; }
    public long getUploadedBytes() { return uploadedBytes; }
    public void setUploadedBytes(long uploadedBytes) { this.uploadedBytes = uploadedBytes; }
    public int getTotalChunks() { return totalChunks; }
    public void setTotalChunks(int totalChunks) { this.totalChunks = totalChunks; }
    public int getCurrentChunk() { return currentChunk; }
    public void setCurrentChunk(int currentChunk) { this.currentChunk = currentChunk; }
    public boolean isRemoved() { return isRemoved; }
    public void setRemoved(boolean removed) { isRemoved = removed; }
    public boolean isInvalid() { return isInvalid; }
    public void setInvalid(boolean invalid) { isInvalid = invalid; }
    public int getIshebing() { return ishebing; }
    public void setIshebing(int ishebing) { this.ishebing = ishebing; }
    public Date getCreatedAt() { return createdAt; }
    public void setCreatedAt(Date createdAt) { this.createdAt = createdAt; }
    
    @NonNull
    @Override
    public String toString() {
        return "Video{" +
                "id='" + id + '\'' +
                ", filePath='" + filePath + '\'' +
                ", status='" + status + '\'' +
                ", taskId=" + taskId +
                '}';
    }
}