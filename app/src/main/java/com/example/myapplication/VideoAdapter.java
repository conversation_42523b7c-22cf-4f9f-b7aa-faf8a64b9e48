package com.example.myapplication;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import okio.Buffer;
import okio.BufferedSink;
import okio.ForwardingSink;
import okio.Okio;

public class VideoAdapter extends RecyclerView.Adapter<VideoAdapter.VideoViewHolder> {

    // 创建Gson实例，用于处理日期格式
    private Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();
    
    private List<Video> videoList;
    private static final int CHUNK_SIZE = 1024 * 1024 * 5; // 5MB
    private static final int MAX_CONCURRENT_UPLOADS = 4;
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private OnItemClickListener onItemClickListener;
    private ApiService apiService; // 使用 ApiService 替代 VideoDAO
    private long taskId;
    private Handler mainHandler = new Handler(Looper.getMainLooper());
    private Map<String, String> uploadIds = new HashMap<>();
    private Map<String, List<Call>> uploadCalls = new HashMap<>();
    private Map<String, Integer> retryCounts = new HashMap<>(); // 跟踪每个视频的重试次数
    private final Semaphore uploadSemaphore = new Semaphore(MAX_CONCURRENT_UPLOADS);
    private Context context;
    private NetworkReceiver networkReceiver;
    private volatile boolean isActive = true; // 是否活跃的标志
    private final Object lock = new Object(); // 用于同步检查
    // 添加批量操作相关变量
    public static boolean isBatchMode = false; // 是否处于批量操作模式
    private List<Video> selectedVideos = new ArrayList<>(); // 已选择的视频

    // 添加一个静态的上传管理器，使其独立于Adapter生命周期
    private static UploadManager uploadManager;

    public VideoAdapter(List<Video> videoList, OnItemClickListener onItemClickListener, ApiService apiService, Context context, long taskId) {
        this.videoList = videoList;
        this.onItemClickListener = onItemClickListener;
        this.apiService = apiService;
        this.context = context;
        this.taskId = taskId;
        
        // 初始化上传管理器（单例模式）
        if (uploadManager == null) {
            uploadManager = UploadManager.getInstance(context);
        }
        
        registerNetworkReceiver(); // 注册网络状态监听
    }

    // 注册网络状态监听器
    private void registerNetworkReceiver() {
        networkReceiver = new NetworkReceiver();
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        context.registerReceiver(networkReceiver, filter);
    }
    

    // 取消注册网络状态监听器
    public void unregisterNetworkReceiver() {
        if (networkReceiver != null) {
            context.unregisterReceiver(networkReceiver);
        }
    }

    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_video, parent, false);
        return new VideoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        Video video = videoList.get(position);
        // 如果 taskId 为 0（表示按 userId 查询），不显示样品编号
        String videoName = taskId == 0
                ? "视频 " + (position + 1)
                : "样品编号: " + video.getTaskId() + " - 视频 " + (position + 1);
        String createdAt = video.getCreatedAt() != null
                ? new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(video.getCreatedAt())
                : "未知时间";
        holder.textViewTitle.setText(videoName + "\n创建时间: " + createdAt);
        holder.textViewStatus.setText(video.getStatus());
        holder.progressBar.setProgress(video.getProgress());

        StringBuilder flags = new StringBuilder();
        if (video.isInvalid()) flags.append("废 ");
        if (video.isRemoved()) flags.append("删 ");
        holder.textViewFlags.setText(flags.toString().trim());

        // Batch mode checkbox
        if (!isBatchMode) {
            VideoListActivity.batchControls.setVisibility(View.GONE);
        }
        holder.checkBoxSelect.setVisibility(isBatchMode ? View.VISIBLE : View.GONE);
        holder.checkBoxSelect.setChecked(selectedVideos.contains(video));
        holder.checkBoxSelect.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                selectedVideos.add(video);
            } else {
                selectedVideos.remove(video);
            }
        });

        holder.buttonOnLineLook.setVisibility(View.GONE);
        if (!video.isRemoved()) {
            holder.buttonLook.setVisibility(View.VISIBLE);
        }

        configureButtonsAndProgress(holder, position, video);
    }

    // 改进 onDestroy 方法
    public void onDestroy() {
        Log.d("VideoAdapter", "onDestroy called");
        isActive = false; // 标记为非活跃状态
        
        // 取消注册网络状态监听器
        unregisterNetworkReceiver();
        
        // 清理所有上传任务
        for (Video video : videoList) {
            if ("上传中".equals(video.getStatus())) {
                pauseUpload(video, videoList.indexOf(video));
            }
        }
        
        // 清理资源
        uploadCalls.clear();
        uploadIds.clear();
        retryCounts.clear();
        
        // 释放所有信号量
        while (uploadSemaphore.availablePermits() < MAX_CONCURRENT_UPLOADS) {
            uploadSemaphore.release();
        }
        
        // 不再关闭线程池，让UploadManager独立管理上传任务
        // executorService.shutdownNow();
    }

    private void configureButtonsAndProgress(VideoViewHolder holder, int position, Video video) {
        if ("待上传".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.VISIBLE);
            holder.buttonCancel.setVisibility(View.VISIBLE);
            holder.buttonDelete.setVisibility(View.GONE);
            holder.buttonContinue.setVisibility(View.GONE);
            holder.buttonPause.setVisibility(View.GONE);
            if (holder.progressBar != null) holder.progressBar.setVisibility(View.GONE);

            holder.buttonUpload.setOnClickListener(v -> {
                startUpload(video, holder, position);
            });
            holder.buttonCancel.setOnClickListener(v -> {
//                video.setStatus("已作废");
                video.setInvalid(true); // 标记为已作废
                updateVideoStatusOnServer(video, position);
            });
        } else if ("上传中".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.GONE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.GONE);
            holder.buttonContinue.setVisibility(View.GONE);
            holder.buttonPause.setVisibility(View.VISIBLE);
            if (holder.progressBar != null) {
                holder.progressBar.setVisibility(View.VISIBLE);
                holder.progressBar.setProgress(video.getProgress());
            }
            holder.textViewStatus.setText("上传中 (" + video.getProgress() + "%)");

            holder.buttonPause.setOnClickListener(v -> pauseUpload(video, position));
        } else if ("已暂停".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.GONE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.VISIBLE);
            holder.buttonContinue.setVisibility(View.VISIBLE);
            holder.buttonPause.setVisibility(View.GONE);
            if (holder.progressBar != null) {
                holder.progressBar.setVisibility(View.VISIBLE);
                holder.progressBar.setProgress(video.getProgress());
            }
            holder.textViewStatus.setText("已暂停 (" + video.getProgress() + "%)");

            holder.buttonContinue.setOnClickListener(v -> {
                resumeUpload(video, holder, position);
            });
            holder.buttonDelete.setOnClickListener(v -> showDeleteConfirmationDialog(holder.itemView.getContext(), position, video));
        } else if ("上传失败".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.GONE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.VISIBLE);
            holder.buttonContinue.setVisibility(View.VISIBLE);
            holder.buttonPause.setVisibility(View.GONE);
            if (holder.progressBar != null) {
                holder.progressBar.setVisibility(View.VISIBLE);
                holder.progressBar.setProgress(video.getProgress());
            }
            holder.textViewStatus.setText("上传失败 (" + video.getProgress() + "%)");

            holder.buttonContinue.setOnClickListener(v -> {
                resumeUpload(video, holder, position);
            });
            holder.buttonDelete.setOnClickListener(v -> showDeleteConfirmationDialog(holder.itemView.getContext(), position, video));
        } else if ("已上传".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.GONE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.VISIBLE);
            holder.buttonContinue.setVisibility(View.GONE);
            holder.buttonPause.setVisibility(View.GONE);
            holder.buttonLook.setVisibility(View.GONE);
            if (holder.progressBar != null) holder.progressBar.setVisibility(View.GONE);
            if(video.getIshebing()==2){
                holder.buttonOnLineLook.setVisibility(View.VISIBLE);
            }
            else if(video.getIshebing()==2){
                holder.textViewFlags.setText("合并中，请等待");
            }
            else{
                holder.buttonOnLineLook.setVisibility(View.GONE);
            }

            holder.buttonDelete.setOnClickListener(v -> showDeleteConfirmationDialog(holder.itemView.getContext(), position, video));
        } else if ("已作废".equals(video.getStatus())) {
            holder.buttonUpload.setVisibility(View.VISIBLE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.GONE);
            holder.buttonContinue.setVisibility(View.GONE);
            holder.buttonPause.setVisibility(View.GONE);
            if (holder.progressBar != null) holder.progressBar.setVisibility(View.GONE);

            holder.buttonUpload.setOnClickListener(v -> {
                startUpload(video, holder, position);
            });
            holder.buttonDelete.setOnClickListener(v -> showDeleteConfirmationDialog(holder.itemView.getContext(), position, video));
        } else {
            holder.buttonUpload.setVisibility(View.GONE);
            holder.buttonCancel.setVisibility(View.GONE);
            holder.buttonDelete.setVisibility(View.GONE);
            holder.buttonContinue.setVisibility(View.GONE);
            holder.buttonPause.setVisibility(View.GONE);
            if (holder.progressBar != null) holder.progressBar.setVisibility(View.GONE);
        }

        holder.buttonLook.setOnClickListener(v -> {
            File videoFile = new File(video.getFilePath());
            Uri videoUri = FileProvider.getUriForFile(
                    holder.itemView.getContext(),
                    "com.example.myapplication.fileprovider",
                    videoFile);
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setDataAndType(videoUri, "video/mp4");
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            holder.itemView.getContext().startActivity(intent);
        });
        holder.buttonOnLineLook.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(video.getPath()));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);

            ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("Video URL", video.getPath());
            clipboard.setPrimaryClip(clip);
            Toast.makeText(context, "链接已放入剪贴板，可分享", Toast.LENGTH_SHORT).show();
                }
        );
    }

    private synchronized void startUpload(Video video, VideoViewHolder holder, int position) {
        // 检查是否已经在上传
        if (uploadIds.containsKey(video.getFilePath())) {
            Log.d("Upload", "Already uploading: " + video.getFilePath());
            return;
        }

        File file = new File(video.getFilePath());
        // 使用稳定的uploadId生成机制，基于文件特征生成MD5哈希作为uploadId
        String uploadId = UploadStateManager.getStableUploadId(context, video.getFilePath(), video.getTaskId());
        if (uploadId == null) {
            // 如果无法生成稳定的uploadId，则回退到原来的方式
            uploadId = file.getAbsolutePath() + "_" + System.currentTimeMillis();
            Log.w("Upload", "Failed to generate stable uploadId, fallback to timestamp method");
        } else {
            Log.d("Upload", "Generated stable uploadId: " + uploadId);
        }
    
        uploadIds.put(video.getFilePath(), uploadId);
        retryCounts.put(video.getFilePath(), 0); // 初始化重试次数

        long fileSize = file.length();
        int totalChunks = (int) Math.ceil((double) fileSize / CHUNK_SIZE);
        video.setTotalChunks(totalChunks);
        video.setStatus("上传中");
        updateVideoStatusOnServer(video, position);

        // 将变量声明为final以在内部类中使用
        final Video finalVideo = video;
        final VideoViewHolder finalHolder = holder;
        final int finalPosition = position;
        final File finalFile = file;
        final int finalTotalChunks = totalChunks;
        final String finalUploadId = uploadId;

        // 先获取本地保存的进度
        int savedProgress = UploadStateManager.getSavedProgress(context, video.getFilePath(), video.getTaskId());
        int savedTotalChunks = UploadStateManager.getSavedTotalChunks(context, video.getFilePath(), video.getTaskId());
    
        Log.d("Upload", "本地保存的进度: " + savedProgress + "/" + savedTotalChunks);

        // 传递uploadId参数给queryUploadedChunksWithOkHttp方法
        try {
            queryUploadedChunksWithOkHttp(file.getName(), uploadId, new OnChunksQueriedListener() {
                @Override
                public void onChunksQueried(List<Integer> uploadedChunks) {
                    Log.d("Upload", "服务器返回已上传分片: " + uploadedChunks);
                    
                    // 以服务器返回的已上传分片为准，不再结合本地保存的进度
                    List<Integer> combinedUploadedChunks = new ArrayList<>(uploadedChunks);
                    
                    Log.d("Upload", "使用服务器返回的已上传分片: " + combinedUploadedChunks);
                    
                    // 标记上传开始
                    UploadStateManager.markUploadStarted(context, finalVideo.getFilePath(), finalVideo.getTaskId());
                    
                    // 使用独立的上传管理器执行上传任务
                    uploadManager.uploadChunks(
                        finalVideo, 
                        finalHolder, 
                        finalPosition, 
                        finalFile, 
                        finalTotalChunks, 
                        finalUploadId, 
                        combinedUploadedChunks,
                        new UploadManager.UploadProgressListener() {
                            @Override
                            public void onProgressUpdated(int progress) {
                                // 更新UI进度
                                mainHandler.post(() -> {
                                    if (finalPosition < videoList.size()) {
                                        videoList.get(finalPosition).setProgress(progress);
                                        videoList.get(finalPosition).setStatus("上传中");
                                        UploadStateManager.saveUploadProgress(
                                            context, 
                                            finalVideo.getFilePath(), 
                                            finalVideo.getTaskId(), 
                                            progress, 
                                            finalTotalChunks
                                        );
                                        updateVideoProgressOnServer(videoList.get(finalPosition), finalPosition);
                                        
                                        // 发送上传进度通知
                                        sendUploadProgressNotification(finalFile.getName(), progress);
                                    }
                                });
                            }
                            
                            @Override
                            public void onUploadCompleted() {
                                // 上传完成处理
                                mainHandler.post(() -> {
                                    if (finalPosition < videoList.size()) {
                                        videoList.get(finalPosition).setStatus("已上传");
                                        UploadStateManager.markUploadCompleted(
                                            context, 
                                            finalVideo.getFilePath(), 
                                            finalVideo.getTaskId()
                                        );
                                        updateVideoStatusOnServer(videoList.get(finalPosition), finalPosition);
                                        
                                        // 取消上传进度通知并发送上传完成通知
                                        cancelUploadProgressNotification(finalFile.getName());
                                        sendUploadCompleteNotification(finalFile.getName());
                                    }
                                });
                            }
                            
                            @Override
                            public void onUploadFailed(String errorMessage) {
                                // 上传失败处理
                                mainHandler.post(() -> {
                                    if (finalPosition < videoList.size()) {
                                        videoList.get(finalPosition).setStatus("上传失败");
                                        updateVideoStatusOnServer(videoList.get(finalPosition), finalPosition);
                                    }
                                });
                            }
                        }
                    );
                }
            });
        } catch (Exception e) {
            Log.e("Upload", "Failed to query uploaded chunks: " + e.getMessage());
        }
    }

    private synchronized void pauseUpload(Video video, int position) {
        String uploadId = uploadIds.get(video.getFilePath());
        if (uploadId != null) {
            // 使用上传管理器暂停上传
            uploadManager.pauseUpload(video.getFilePath());
            
            uploadCalls.remove(video.getFilePath());
            uploadIds.remove(video.getFilePath());
            retryCounts.remove(video.getFilePath());

            Log.d("Upload", "Paused upload: " + uploadId);
        }

        video.setStatus("已暂停");
        updateVideoStatusOnServer(video, position);
        
        // 取消上传进度通知
        File videoFile = new File(video.getFilePath());
        cancelUploadProgressNotification(videoFile.getName());
    }

    private void resumeUpload(Video video, VideoViewHolder holder, int position) {
        if (!isNetworkAvailable()) {
            Toast.makeText(context, "网络不可用，请检查网络连接", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 检查是否已经在上传队列中
        if (uploadIds.containsKey(video.getFilePath())) {
            Log.d("Upload", "Upload already in progress for: " + video.getFilePath());
            return; // 如果已经在上传，直接返回
        }
        
        // 清除该视频的暂停状态
        uploadManager.resumeUpload(video.getFilePath());
        
        startUpload(video, holder, position); // 复用 startUpload 逻辑
    }

    // 私有方法uploadChunks被注释掉，现在由UploadManager处理上传任务
    // private void uploadChunks(Video video, VideoViewHolder holder, int position, File file, int totalChunks, String uploadId, List<Integer> uploadedChunks) {
    //     // 上传逻辑
    // }

    // 私有方法uploadChunkWithOkHttp被注释掉，现在由UploadManager处理上传任务
    // private Call uploadChunkWithOkHttp(Video video, VideoViewHolder holder, int position, File file, int chunk, int totalChunks, String uploadId, OnUploadCompleteListener completeListener, ProgressRequestBody.OnUploadResponseListener responseListener) {
    //     // OkHttp上传分片逻辑
    // }

    private void queryUploadedChunksWithOkHttp(String fileName, String uploadId, OnChunksQueriedListener listener) {
        OkHttpClient client = new OkHttpClient();
        try {
            // 添加uploadId参数到查询URL
            String url = Config.BASE_URL + "upload/status?fileName=" + URLEncoder.encode(fileName, "UTF-8") + "&uploadId=" + uploadId;
            Request request = new Request.Builder().url(url).get().build();

            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e("Upload", "Query uploaded chunks failed: " + e.getMessage());
                    mainHandler.post(() -> listener.onChunksQueried(new ArrayList<>()));
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.isSuccessful()) {
                        String responseBody = response.body().string();
                        try {
                            JSONObject json = new JSONObject(responseBody);
                            JSONArray chunksArray = json.getJSONArray("uploadedChunks");
                            List<Integer> uploadedChunks = new ArrayList<>();
                            for (int i = 0; i < chunksArray.length(); i++) {
                                uploadedChunks.add(chunksArray.getInt(i));
                            }
                            mainHandler.post(() -> listener.onChunksQueried(uploadedChunks));
                        } catch (JSONException e) {
                            Log.e("Upload", "JSON parsing error: " + e.getMessage());
                            mainHandler.post(() -> listener.onChunksQueried(new ArrayList<>()));
                        }
                    } else {
                        Log.e("Upload", "Query uploaded chunks failed: " + response.message());
                        mainHandler.post(() -> listener.onChunksQueried(new ArrayList<>()));
                    }
                    response.close();
                }
            });
        } catch (Exception e) {
            Log.e("Upload", "URL encoding failed: " + e.getMessage());
            mainHandler.post(() -> listener.onChunksQueried(new ArrayList<>()));
        }
    }

    private String extractUrlFromResponse(String responseBody, Video video) {
        String prefix = "文件上传成功，访问 URL: ";
        int startIndex = responseBody.indexOf(prefix) + prefix.length();
        if (startIndex >= prefix.length()) {
            return responseBody.substring(startIndex).trim();
        }
        return Config.BASE_URL + "public/uploadedMovie/" + new File(video.getFilePath()).getName();
    }

    private void showUploadSuccessDialog(Context context, String videoUrl) {
        if (context instanceof Activity && !((Activity) context).isFinishing() && !((Activity) context).isDestroyed()) {
            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            builder.setMessage("上传成功，是否需要打开链接在线观看？")
                    .setPositiveButton("观看", (dialog, id) -> {
                        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(videoUrl));
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(intent);

                        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                        ClipData clip = ClipData.newPlainText("Video URL", videoUrl);
                        clipboard.setPrimaryClip(clip);
                        Toast.makeText(context, "链接已放入剪贴板，可分享", Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton("取消", (dialog, id) -> dialog.dismiss());
            mainHandler.post(() -> builder.create().show());
        } else {
            showUploadSuccessNotification(context.getApplicationContext(), videoUrl);
        }
    }

    private void showUploadSuccessNotification(Context context, String videoUrl) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        String channelId = "upload_success_channel";

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelId, "Upload Success", NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
        }

        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(videoUrl));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle("视频上传成功")
                .setContentText("点击查看: " + videoUrl)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent);

        notificationManager.notify((int) System.currentTimeMillis(), builder.build());

        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("Video URL", videoUrl);
        clipboard.setPrimaryClip(clip);
    }

    // 添加发送上传完成通知的方法
    private void sendUploadCompleteNotification(String videoFileName) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        String channelId = "upload_complete_channel";

        // 创建通知渠道（Android 8.0+需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    channelId, 
                    "Upload Complete", 
                    NotificationManager.IMPORTANCE_HIGH
            );
            notificationManager.createNotificationChannel(channel);
        }

        // 构建通知
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
                .setSmallIcon(android.R.drawable.ic_menu_upload)
                .setContentTitle("视频上传完成")
                .setContentText("视频\"" + videoFileName + "\"已成功上传")
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_HIGH);

        // 显示通知
        notificationManager.notify((int) System.currentTimeMillis(), builder.build());
    }

    // 添加发送上传进度通知的方法
    private void sendUploadProgressNotification(String videoFileName, int progress) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        String channelId = "upload_progress_channel";
        int notificationId = videoFileName.hashCode(); // 使用文件名的hash作为通知ID，确保同一文件只有一个进度通知

        // 创建通知渠道（Android 8.0+需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    channelId, 
                    "Upload Progress", 
                    NotificationManager.IMPORTANCE_LOW
            );
            notificationManager.createNotificationChannel(channel);
        }

        // 构建通知
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelId)
                .setSmallIcon(android.R.drawable.ic_menu_upload)
                .setContentTitle("正在上传视频")
                .setContentText("视频\"" + videoFileName + "\"上传进度: " + progress + "%")
                .setProgress(100, progress, false)
                .setOngoing(true) // 设置为正在进行的通知
                .setPriority(NotificationCompat.PRIORITY_LOW);

        // 显示通知
        notificationManager.notify(notificationId, builder.build());
    }
    
    // 添加取消上传进度通知的方法
    private void cancelUploadProgressNotification(String videoFileName) {
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        int notificationId = videoFileName.hashCode();
        notificationManager.cancel(notificationId);
    }
    
    public boolean isUploading(String filePath) {
        return uploadIds.containsKey(filePath);
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

    public void addVideo(Video video) {
        videoList.add(video);
        notifyItemInserted(videoList.size() - 1);
    }

    public void updateVideos(List<Video> newVideos) {
        this.videoList.clear();
        this.videoList.addAll(newVideos);
        notifyDataSetChanged();
    }

    // 添加公共的批量上传方法
    public void batchUpload() {
        batchUploadSelectedVideos();
    }

    // 添加公共的批量删除方法
    public void batchDelete() {
        batchDeleteSelectedVideos();
    }

    private void showDeleteConfirmationDialog(Context context, int position, Video video) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("确定要删除这个视频吗？")
                .setPositiveButton("删除", (dialog, id) -> {
                    File videoFile = new File(video.getFilePath());
                    video.setRemoved(true); // 标记为已删除
                    uploadIds.remove(video.getFilePath());
                    uploadCalls.remove(video.getFilePath());
                    updateVideoOnServer(video, position);
                    if (videoFile.exists() && videoFile.delete()) {
                        Toast.makeText(context, "删除成功！且本地文件也一并被删除了", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(context, "删除成功！但本地文件已不存在", Toast.LENGTH_SHORT).show();
                    }

                })
                .setNegativeButton("取消", (dialog, id) -> dialog.dismiss());
        builder.create().show();

//        builder.setMessage("确定要删除这个视频吗？")
//                .setPositiveButton("删除", (dialog, id) -> {
//                    video.setRemoved(true); // 标记为已删除
//                    uploadIds.remove(video.getFilePath());
//                    uploadCalls.remove(video.getFilePath());
//                    updateVideoOnServer(video, position);
//                })
//                .setNegativeButton("取消", (dialog, id) -> dialog.dismiss());
//        builder.create().show();
    }

    private void showFileNotExistDialog(Context context, int position, Video video) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("文件不存在，是否从服务器中删除？")
                .setPositiveButton("删除", (dialog, id) -> {
                    deleteVideoFromServer(video, position);
                })
                .setNegativeButton("取消", (dialog, id) -> dialog.dismiss());
        builder.create().show();
    }
    // 切换批量操作模式
    public void toggleBatchMode(boolean enable) {
        isBatchMode = enable;
        selectedVideos.clear();
        notifyDataSetChanged();
    }

    // 全选
    public void selectAll() {
        selectedVideos.clear();
        selectedVideos.addAll(videoList);
        notifyDataSetChanged();
    }

    // 批量上传视频
    private void batchUploadSelectedVideos() {
        List<Video> videosToUpload = new ArrayList<>();
        for (Video video : selectedVideos) {
            if ("待上传".equals(video.getStatus()) || "已作废".equals(video.getStatus())) {
                videosToUpload.add(video);
            }
        }

        for (Video video : videosToUpload) {
            int position = videoList.indexOf(video);
            if (position != -1) {
                // 直接调用上传方法，不依赖ViewHolder
                startUpload(video, null, position);
            }
        }
        
        // 关闭批量模式
        if (VideoListActivity.buttonToggleBatchMode != null) {
            VideoListActivity.buttonToggleBatchMode.callOnClick();
        }
    }

    // 批量删除视频
    private void batchDeleteSelectedVideos() {
        new AlertDialog.Builder(context)
                .setMessage("确定要删除 " + selectedVideos.size() + " 个视频吗？")
                .setPositiveButton("确定", (dialog, which) -> {
                    for (Video video : new ArrayList<>(selectedVideos)) {
                        int position = videoList.indexOf(video);
                        if (position != -1) {
                            // 标记为已删除
                            video.setRemoved(true); // 标记为已删除
                            uploadIds.remove(video.getFilePath());
                            uploadCalls.remove(video.getFilePath());

                            // 删除本地文件
                            File videoFile = new File(video.getFilePath());
                            if (videoFile.exists() && videoFile.delete()) {
                                Toast.makeText(context, "删除成功！且本地文件也一并被删除了", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(context, "删除成功！但本地文件已不存在", Toast.LENGTH_SHORT).show();
                            }

                            // 更新服务器状态
                            updateVideoOnServer(video, position);

                            // 从列表中移除
                            videoList.remove(position);
                            notifyItemRemoved(position);
                        }
                    }
                    selectedVideos.clear();
                    
                    // 关闭批量模式
                    if (VideoListActivity.buttonToggleBatchMode != null) {
                        VideoListActivity.buttonToggleBatchMode.callOnClick();
                    }
                })
                .setNegativeButton("取消", (dialog, id) -> dialog.dismiss())
                .create()
                .show();
    }
    @Override
    public void onViewRecycled(@NonNull VideoViewHolder holder) {
        super.onViewRecycled(holder);
        holder.checkBoxSelect.setOnCheckedChangeListener(null); // 防止复用时监听器重复触发
    }
    public class VideoViewHolder extends RecyclerView.ViewHolder {
        TextView textViewTitle, textViewStatus, textViewFlags;
        Button buttonUpload, buttonCancel, buttonDelete, buttonLook, buttonContinue, buttonPause,buttonOnLineLook;
        private LinearLayout batchControls;
        ProgressBar progressBar;
        CheckBox checkBoxSelect; // 添加复选框

        public VideoViewHolder(View itemView) {
            super(itemView);
            textViewTitle = itemView.findViewById(R.id.textViewTitle);
            textViewStatus = itemView.findViewById(R.id.textViewStatus);
            textViewFlags = itemView.findViewById(R.id.textViewFlags);
            buttonUpload = itemView.findViewById(R.id.buttonUpload);
            buttonCancel = itemView.findViewById(R.id.buttonCancel);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
            buttonLook = itemView.findViewById(R.id.buttonLook);
            buttonOnLineLook=itemView.findViewById(R.id.buttonOnLineLook);
            buttonContinue = itemView.findViewById(R.id.buttonContinue);
            buttonPause = itemView.findViewById(R.id.buttonPause);
            progressBar = itemView.findViewById(R.id.progressBar);
            checkBoxSelect = itemView.findViewById(R.id.checkBoxSelect); // 初始化复选框
            batchControls=VideoListActivity.batchControls;
            if (progressBar == null) {
                Log.e("VideoAdapter", "ProgressBar is null in VideoViewHolder");
            }
        }
    }

    public interface OnItemClickListener {
        void onItemClick(Video video);
    }

    interface OnChunksQueriedListener {
        void onChunksQueried(List<Integer> uploadedChunks);
    }

    interface OnUploadCompleteListener {
        void onComplete();
    }

    public static class ProgressRequestBody extends RequestBody {
        private final RequestBody delegate;
        private final ProgressListener listener;

        public ProgressRequestBody(RequestBody delegate, ProgressListener listener) {
            this.delegate = delegate;
            this.listener = listener;
        }

        @Override
        public MediaType contentType() {
            return delegate.contentType();
        }

        @Override
        public long contentLength() throws IOException {
            return delegate.contentLength();
        }

        @Override
        public void writeTo(BufferedSink sink) throws IOException {
            BufferedSink bufferedSink = Okio.buffer(new ForwardingSink(sink) {
                private long bytesWritten = 0L;

                @Override
                public void write(Buffer source, long byteCount) throws IOException {
                    super.write(source, byteCount);
                    bytesWritten += byteCount;
                    listener.onProgress(bytesWritten, contentLength());
                }
            });
            delegate.writeTo(bufferedSink);
            bufferedSink.flush();
        }

        public interface ProgressListener {
            void onProgress(long bytesWritten, long contentLength);
        }

        interface OnUploadResponseListener {
            void onResponse(String responseBody);
        }
    }

    // 私有方法handleUploadFailure被注释掉，现在由UploadManager处理上传任务
    // private void handleUploadFailure(Video video, VideoViewHolder holder, int position, int chunk, int totalChunks, String uploadId) {
    //     // 处理上传失败逻辑
    // }

    private boolean isNetworkAvailable() {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = cm.getActiveNetworkInfo();
        return networkInfo != null && networkInfo.isConnected();
    }

    // 暂停所有上传任务
    private void pauseAllUploads() {
        uploadManager.pauseAllUploads(videoList);
    }

    public void resumeAllUploads() {
        for (Video video : videoList) {
            if ("已暂停".equals(video.getStatus()) || "上传失败".equals(video.getStatus())) {
                try {
                    resumeUpload(video, null, videoList.indexOf(video));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private class NetworkReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (isNetworkAvailable()) {
                Log.d("Network", "Network restored, checking for failed or paused uploads");
                // 使用UploadManager恢复上传
                uploadManager.resumeAllUploads(videoList);
            } else {
                Log.d("Network", "Network lost");
                // 使用UploadManager暂停上传
                uploadManager.pauseAllUploads(videoList);
            }
        }
    }

    // 辅助方法：更新视频状态到服务器
    private void updateVideoStatusOnServer(Video video, int position) {
        apiService.updateVideoStatus(video).enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(retrofit2.Call<Void> call, retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    mainHandler.post(() -> notifyItemChanged(position));
                } else {
                    Log.e("VideoAdapter", "更新状态失败: " + response.message());
                }
            }

            @Override
            public void onFailure(retrofit2.Call<Void> call, Throwable t) {
                Log.e("VideoAdapter", "更新状态失败: " + t.getMessage());
            }
        });
    }

    // 辅助方法：更新视频进度到服务器
    private void updateVideoProgressOnServer(Video video, int position) {
        apiService.updateVideoProgress(video.getId(), video.getProgress()).enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(retrofit2.Call<Void> call, retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    mainHandler.post(() -> notifyItemChanged(position));
                } else {
                    Log.e("VideoAdapter", "更新进度失败: " + response.message());
                }
            }

            @Override
            public void onFailure(retrofit2.Call<Void> call, Throwable t) {
                Log.e("VideoAdapter", "更新进度失败: " + t.getMessage());
            }
        });
    }

    // 辅助方法：更新视频到服务器
    private void updateVideoOnServer(Video video, int position) {
        // Create a new Video object with only required fields to avoid sending createdAt
        Video updateVideo = new Video(video.getFilePath(), video.getStatus(), video.getTaskId());
        updateVideo.setId(video.getId());
        updateVideo.setPath(video.getPath());
        updateVideo.setProgress(video.getProgress());
        updateVideo.setUploadedBytes(video.getUploadedBytes());
        updateVideo.setTotalChunks(video.getTotalChunks());
        updateVideo.setCurrentChunk(video.getCurrentChunk());
        updateVideo.setRemoved(video.isRemoved());
        updateVideo.setInvalid(video.isInvalid());
        updateVideo.setIshebing(video.getIshebing());
        apiService.updateVideoStatus(updateVideo).enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(retrofit2.Call<Void> call, retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    mainHandler.post(() -> notifyItemChanged(position));
                } else {
                    Log.e("VideoAdapter", "更新视频失败: " + response.message());
                }
            }

            @Override
            public void onFailure(retrofit2.Call<Void> call, Throwable t) {
                Log.e("VideoAdapter", "更新视频失败: " + t.getMessage());
            }
        });
    }

    // 辅助方法：从服务器删除视频
    private void deleteVideoFromServer(Video video, int position) {
        apiService.deleteVideo(video.getId()).enqueue(new retrofit2.Callback<Void>() {
            @Override
            public void onResponse(retrofit2.Call<Void> call, retrofit2.Response<Void> response) {
                if (response.isSuccessful()) {
                    videoList.remove(position);
                    mainHandler.post(() -> notifyItemRemoved(position));
                } else {
                    Log.e("VideoAdapter", "删除视频失败: " + response.message());
                }
            }

            @Override
            public void onFailure(retrofit2.Call<Void> call, Throwable t) {
                Log.e("VideoAdapter", "删除视频失败: " + t.getMessage());
            }
        });
    }
}