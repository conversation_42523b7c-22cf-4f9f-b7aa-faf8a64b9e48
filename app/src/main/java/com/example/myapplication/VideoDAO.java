package com.example.myapplication;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class VideoDAO {
    private SQLiteDatabase db;
    private VideoDatabaseHelper dbHelper;
    private final Object dbLock = new Object(); // 用于线程同步
    private volatile boolean isClosed = false; // 标记数据库是否已关闭

    public VideoDAO(Context context) {
        dbHelper = new VideoDatabaseHelper(context);
    }

    public void open() throws SQLException {
        synchronized (dbLock) {
            if (db == null || !db.isOpen()) {
                db = dbHelper.getWritableDatabase();
                isClosed = false;
                Log.d("VideoDAO", "Database opened");
            }
        }
    }

    public void close() {
        synchronized (dbLock) {
            if (db != null && db.isOpen()) {
                db.close();
                isClosed = true;
                Log.d("VideoDAO", "Database closed");
            }
        }
    }

    // 检查数据库是否可用
    public boolean isDatabaseOpen() {
        synchronized (dbLock) {
            return db != null && db.isOpen() && !isClosed;
        }
    }

    public long insertVideo(Video video) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot insert video: " + video.getFilePath());
                return -1; // 返回失败
            }
            ContentValues values = new ContentValues();
            values.put(VideoDatabaseHelper.COLUMN_FILE_PATH, video.getFilePath());
            values.put(VideoDatabaseHelper.COLUMN_STATUS, video.getStatus());
            values.put(VideoDatabaseHelper.COLUMN_TASK_ID, video.getTaskId());
            values.put(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES, video.getUploadedBytes());
            values.put(VideoDatabaseHelper.COLUMN_PROGRESS, video.getProgress());
            values.put(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS, video.getTotalChunks());
            values.put(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK, video.getCurrentChunk());
            return db.insert(VideoDatabaseHelper.TABLE_VIDEOS, null, values);
        }
    }

    public void insertOrUpdateVideo(Video video) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot insert/update video: " + video.getFilePath());
                return;
            }
            ContentValues values = new ContentValues();
            values.put(VideoDatabaseHelper.COLUMN_FILE_PATH, video.getFilePath());
            values.put(VideoDatabaseHelper.COLUMN_STATUS, video.getStatus());
            values.put(VideoDatabaseHelper.COLUMN_TASK_ID, video.getTaskId());
            values.put(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES, video.getUploadedBytes());
            values.put(VideoDatabaseHelper.COLUMN_PROGRESS, video.getProgress());
            values.put(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS, video.getTotalChunks());
            values.put(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK, video.getCurrentChunk());

            int rows = db.update(VideoDatabaseHelper.TABLE_VIDEOS, values,
                    VideoDatabaseHelper.COLUMN_FILE_PATH + "=?", new String[]{video.getFilePath()});
            if (rows == 0) {
                db.insert(VideoDatabaseHelper.TABLE_VIDEOS, null, values);
            }
        }
    }

    public List<Video> getVideosForTask(int taskId, String status) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, returning empty list for taskId: " + taskId);
                return new ArrayList<>();
            }
            List<Video> videoList = new ArrayList<>();
            String selection = VideoDatabaseHelper.COLUMN_TASK_ID + "=? AND " + VideoDatabaseHelper.COLUMN_STATUS + "=?";
            String[] selectionArgs = new String[]{String.valueOf(taskId), status};

            Cursor cursor = null;
            try {
                cursor = db.query(
                        VideoDatabaseHelper.TABLE_VIDEOS, null, selection, selectionArgs, null, null, null
                );

                int filePathIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_FILE_PATH);
                int statusIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_STATUS);
                int taskIdIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TASK_ID);
                int uploadedBytesIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES);
                int progressIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_PROGRESS);
                int totalChunksIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS);
                int currentChunkIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK);

                if (cursor.moveToFirst()) {
                    do {
                        Video video = new Video(
                                cursor.getString(filePathIndex),
                                cursor.getString(statusIndex),
                                cursor.getInt(taskIdIndex)
                        );
                        video.setUploadedBytes(cursor.getLong(uploadedBytesIndex));
                        video.setProgress(cursor.getInt(progressIndex));
                        video.setTotalChunks(cursor.getInt(totalChunksIndex));
                        video.setCurrentChunk(cursor.getInt(currentChunkIndex));
                        videoList.add(video);
                    } while (cursor.moveToNext());
                }
            } catch (Exception e) {
                Log.e("VideoDAO", "Error querying videos: " + e.getMessage());
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return videoList;
        }
    }
    public List<Video> getAllVideosForTask(int taskId) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, returning empty list for taskId: " + taskId);
                return new ArrayList<>();
            }
            List<Video> videoList = new ArrayList<>();
            String selection = VideoDatabaseHelper.COLUMN_TASK_ID + "=?";
            String[] selectionArgs = new String[]{String.valueOf(taskId)};

            Cursor cursor = null;
            try {
                cursor = db.query(
                        VideoDatabaseHelper.TABLE_VIDEOS, null, selection, selectionArgs, null, null, null
                );

                int filePathIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_FILE_PATH);
                int statusIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_STATUS);
                int taskIdIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TASK_ID);
                int uploadedBytesIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES);
                int progressIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_PROGRESS);
                int totalChunksIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS);
                int currentChunkIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK);

                if (cursor.moveToFirst()) {
                    do {
                        Video video = new Video(
                                cursor.getString(filePathIndex),
                                cursor.getString(statusIndex),
                                cursor.getInt(taskIdIndex)
                        );
                        video.setUploadedBytes(cursor.getLong(uploadedBytesIndex));
                        video.setProgress(cursor.getInt(progressIndex));
                        video.setTotalChunks(cursor.getInt(totalChunksIndex));
                        video.setCurrentChunk(cursor.getInt(currentChunkIndex));
                        videoList.add(video);
                    } while (cursor.moveToNext());
                }
            } catch (Exception e) {
                Log.e("VideoDAO", "Error querying videos: " + e.getMessage());
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return videoList;
        }
    }
    public Video getVideoByTaskIdAndPath(int taskId, String filePath) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot get video for taskId: " + taskId + ", filePath: " + filePath);
                return null;
            }
            String selection = VideoDatabaseHelper.COLUMN_TASK_ID + "=? AND " + VideoDatabaseHelper.COLUMN_FILE_PATH + "=?";
            String[] selectionArgs = new String[]{String.valueOf(taskId), filePath};

            Cursor cursor = null;
            Video video = null;
            try {
                cursor = db.query(
                        VideoDatabaseHelper.TABLE_VIDEOS, null, selection, selectionArgs, null, null, null
                );

                if (cursor.moveToFirst()) {
                    int filePathIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_FILE_PATH);
                    int statusIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_STATUS);
                    int taskIdIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TASK_ID);
                    int uploadedBytesIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES);
                    int progressIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_PROGRESS);
                    int totalChunksIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS);
                    int currentChunkIndex = cursor.getColumnIndex(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK);

                    video = new Video(
                            cursor.getString(filePathIndex),
                            cursor.getString(statusIndex),
                            cursor.getInt(taskIdIndex)
                    );
                    video.setUploadedBytes(cursor.getLong(uploadedBytesIndex));
                    video.setProgress(cursor.getInt(progressIndex));
                    video.setTotalChunks(cursor.getInt(totalChunksIndex));
                    video.setCurrentChunk(cursor.getInt(currentChunkIndex));
                }
            } catch (Exception e) {
                Log.e("VideoDAO", "Error querying video: " + e.getMessage());
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return video;
        }
    }

    public void updateVideoStatus(Video video) {
        Log.d("StatusTrace", "From " + video.getStatus() + " to " );
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot update video status: " + video.getFilePath());
                return;
            }
            ContentValues values = new ContentValues();
            values.put(VideoDatabaseHelper.COLUMN_STATUS, video.getStatus());
            values.put(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES, video.getUploadedBytes());
            values.put(VideoDatabaseHelper.COLUMN_PROGRESS, video.getProgress());
            values.put(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS, video.getTotalChunks());
            values.put(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK, video.getCurrentChunk());

            db.update(VideoDatabaseHelper.TABLE_VIDEOS, values,
                    VideoDatabaseHelper.COLUMN_FILE_PATH + "=?", new String[]{video.getFilePath()});
        }
    }
    public void updateVideoProgress(Video video) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot update video status: " + video.getFilePath());
                return;
            }
            ContentValues values = new ContentValues();
            values.put(VideoDatabaseHelper.COLUMN_UPLOADED_BYTES, video.getUploadedBytes());
            values.put(VideoDatabaseHelper.COLUMN_PROGRESS, video.getProgress());
            values.put(VideoDatabaseHelper.COLUMN_TOTAL_CHUNKS, video.getTotalChunks());
            values.put(VideoDatabaseHelper.COLUMN_CURRENT_CHUNK, video.getCurrentChunk());

            db.update(VideoDatabaseHelper.TABLE_VIDEOS, values,
                    VideoDatabaseHelper.COLUMN_FILE_PATH + "=?", new String[]{video.getFilePath()});
        }
    }

    public void deleteVideo(String filePath) {
        synchronized (dbLock) {
            if (!isDatabaseOpen()) {
                Log.w("VideoDAO", "Database closed, cannot delete video: " + filePath);
                return;
            }
            db.delete(VideoDatabaseHelper.TABLE_VIDEOS,
                    VideoDatabaseHelper.COLUMN_FILE_PATH + "=?", new String[]{filePath});
        }
    }
}