package com.example.myapplication;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class VideoDatabaseHelper extends SQLiteOpenHelper {
    private static final String DATABASE_NAME = "videos.db";
    private static final int DATABASE_VERSION = 3; // 更新版本号

    public static final String TABLE_VIDEOS = "videos";
    public static final String COLUMN_ID = "_id";
    public static final String COLUMN_FILE_PATH = "file_path";
    public static final String COLUMN_STATUS = "status";
    public static final String COLUMN_TASK_ID = "task_id";
    public static final String COLUMN_UPLOADED_BYTES = "uploaded_bytes";
    public static final String COLUMN_PROGRESS = "progress";
    public static final String COLUMN_TOTAL_CHUNKS = "total_chunks";
    public static final String COLUMN_CURRENT_CHUNK = "current_chunk";

    private static final String DATABASE_CREATE = "CREATE TABLE " + TABLE_VIDEOS + "(" +
            COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
            COLUMN_FILE_PATH + " TEXT, " +
            COLUMN_STATUS + " TEXT, " +
            COLUMN_TASK_ID + " INTEGER, " +
            COLUMN_UPLOADED_BYTES + " INTEGER DEFAULT 0, " +
            COLUMN_PROGRESS + " INTEGER DEFAULT 0, " +
            COLUMN_TOTAL_CHUNKS + " INTEGER DEFAULT 0, " +
            COLUMN_CURRENT_CHUNK + " INTEGER DEFAULT 0" +
            ");";

    public VideoDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(DATABASE_CREATE);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        if (oldVersion < 2) {
            db.execSQL("ALTER TABLE " + TABLE_VIDEOS + " ADD COLUMN " + COLUMN_UPLOADED_BYTES + " INTEGER DEFAULT 0");
            db.execSQL("ALTER TABLE " + TABLE_VIDEOS + " ADD COLUMN " + COLUMN_PROGRESS + " INTEGER DEFAULT 0");
        }
        if (oldVersion < 3) {
            db.execSQL("ALTER TABLE " + TABLE_VIDEOS + " ADD COLUMN " + COLUMN_TOTAL_CHUNKS + " INTEGER DEFAULT 0");
            db.execSQL("ALTER TABLE " + TABLE_VIDEOS + " ADD COLUMN " + COLUMN_CURRENT_CHUNK + " INTEGER DEFAULT 0");
        }
    }
}