package com.example.myapplication;

import java.util.Objects;

public class VideoKey {
    private String videoPath;
    private long taskId;

    public VideoKey(String videoPath, long taskId) {
        this.videoPath = videoPath;
        this.taskId = taskId;
    }

    public String getVideoPath() {
        return videoPath;
    }

    public long getTaskId() {
        return taskId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VideoKey videoKey = (VideoKey) o;
        return taskId == videoKey.taskId &&
                Objects.equals(videoPath, videoKey.videoPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(videoPath, taskId);
    }
}
