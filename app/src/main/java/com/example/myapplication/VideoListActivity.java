package com.example.myapplication;

import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class VideoListActivity extends AppCompatActivity {

    protected RecyclerView recyclerViewVideos;
    protected VideoAdapter videoAdapter;
    protected Button buttonRecordNewVideo;
    protected long taskId;
    protected String userId;
    protected ApiService apiService;
    protected String currentTab = "待上传";
    public static LinearLayout batchControls;
    public static Button buttonToggleBatchMode;
    private Button buttonBatchUpload;
    private Button buttonBatchDelete;
    private static final String TAG = "VideoListActivity";
    private static final int REQUEST_CODE_RECORD_VIDEO = 1;
    private boolean autoRecordTriggered = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(R.layout.activity_video_list);
        Log.d(TAG, "setContentView: activity_video_list");

        // 创建Gson实例，用于处理日期格式
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(Config.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        apiService = retrofit.create(ApiService.class);
        Log.d(TAG, "apiService 初始化完成");

        // 从 savedInstanceState 恢复 userId 和 taskId
        if (savedInstanceState != null) {
            userId = savedInstanceState.getString("userId");
            taskId = savedInstanceState.getLong("taskId", -1L);
            Log.d(TAG, "从 savedInstanceState 恢复 userId=" + userId + ", taskId=" + taskId);
        } else {
            taskId = getIntent().getLongExtra("taskId", -1L);
            userId = getIntent().getStringExtra("userId");
            Log.d(TAG, "从 Intent 获取 userId=" + userId + ", taskId=" + taskId);
        }

        // 验证 userId
        if (userId == null || userId.trim().isEmpty()) {
            Log.e(TAG, "userId 缺失，关闭活动");
            Toast.makeText(this, "缺少用户ID，无法继续", Toast.LENGTH_LONG).show();
            finish();
            return;
        }

        // 如果是 UserVideoListActivity 的实例，直接返回
        if (this instanceof UserVideoListActivity) {
            Log.d(TAG, "已在 UserVideoListActivity，跳过跳转");
            initializeViews();
            return;
        }

        // 如果 taskId 无效，跳转到 UserVideoListActivity
        if (taskId == -1L) {
            Log.d(TAG, "无效 taskId，跳转到 UserVideoListActivity: userId=" + userId);
            Intent intent = new Intent(this, UserVideoListActivity.class);
            intent.putExtra("userId", userId);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            startActivity(intent);
            finish();
            return;
        }

        initializeViews();
    }

    private void initializeViews() {
        // 初始化 RecyclerView
        recyclerViewVideos = findViewById(R.id.recycler_view_videos);
        if (recyclerViewVideos == null) {
            Log.e(TAG, "recyclerViewVideos 未找到，检查 activity_video_list.xml");
            finish();
            return;
        }
        recyclerViewVideos.setLayoutManager(new LinearLayoutManager(this));

        buttonRecordNewVideo = findViewById(R.id.button_record_new_video);
        if (buttonRecordNewVideo == null) {
            Log.e(TAG, "buttonRecordNewVideo 未找到，检查 activity_video_list.xml");
            finish();
            return;
        }
        buttonRecordNewVideo.setOnClickListener(v -> {
            if (userId == null || userId.trim().isEmpty()) {
                Log.e(TAG, "启动 VideoRecorderActivity 失败：userId 为空");
                Toast.makeText(this, "缺少用户ID，无法录制视频", Toast.LENGTH_SHORT).show();
                return;
            }
            Intent intent = new Intent(this, VideoRecorderActivity.class);
            intent.putExtra("userId", userId);
            intent.putExtra("taskId", taskId);
            Log.d(TAG, "启动 VideoRecorderActivity: userId=" + userId + ", taskId=" + taskId);
            startActivityForResult(intent, REQUEST_CODE_RECORD_VIDEO);
        });

        batchControls = findViewById(R.id.batchControls);
        buttonToggleBatchMode = findViewById(R.id.buttonToggleBatchMode);
        buttonBatchUpload = findViewById(R.id.buttonBatchUpload);
        buttonBatchDelete = findViewById(R.id.buttonBatchDelete);

        // 初始化 videoAdapter
        videoAdapter = new VideoAdapter(new ArrayList<>(), video -> {
            File videoFile = new File(video.getFilePath());
            Uri videoUri = FileProvider.getUriForFile(
                    VideoListActivity.this,
                    "com.example.myapplication.fileprovider",
                    videoFile);
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setDataAndType(videoUri, "video/mp4");
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(intent);
        }, apiService, this, taskId);
        recyclerViewVideos.setAdapter(videoAdapter);
        Log.d(TAG, "videoAdapter 初始化完成");

        // 设置批量操作按钮
        if (buttonToggleBatchMode != null) {
            buttonToggleBatchMode.setOnClickListener(v -> {
                boolean isBatchMode = !VideoAdapter.isBatchMode;
                videoAdapter.toggleBatchMode(isBatchMode);
                batchControls.setVisibility(isBatchMode ? View.VISIBLE : View.GONE);
                buttonToggleBatchMode.setText(isBatchMode ? "退出批量操作模式" : "进入批量操作模式");
                buttonRecordNewVideo.setVisibility(isBatchMode ? View.GONE : View.VISIBLE);
                if (currentTab.equals("待上传")) {
                    buttonBatchUpload.setVisibility(View.VISIBLE);
                    buttonBatchDelete.setVisibility(View.GONE);
                } else {
                    buttonBatchUpload.setVisibility(View.GONE);
                    buttonBatchDelete.setVisibility(View.VISIBLE);
                }
                loadVideos(currentTab);
            });
        }

        // 设置其他按钮
        if (findViewById(R.id.buttonSelectAll) != null) {
            findViewById(R.id.buttonSelectAll).setOnClickListener(v -> videoAdapter.selectAll());
        }
        if (findViewById(R.id.buttonBatchUpload) != null) {
            findViewById(R.id.buttonBatchUpload).setOnClickListener(v -> videoAdapter.batchUpload());
        }
        if (findViewById(R.id.buttonBatchDelete) != null) {
            findViewById(R.id.buttonBatchDelete).setOnClickListener(v -> videoAdapter.batchDelete());
        }
        if (findViewById(R.id.buttonCancelSelection) != null) {
            findViewById(R.id.buttonCancelSelection).setOnClickListener(v -> {
                videoAdapter.toggleBatchMode(false);
                batchControls.setVisibility(View.GONE);
                buttonToggleBatchMode.setText("进入批量操作模式");
                buttonRecordNewVideo.setVisibility(View.VISIBLE);
            });
        }

        loadVideos(currentTab);
        setupTabListeners();
        updateTabUI();

        // 检查是否需要自动录像（仅在首次创建时检查）
        boolean autoRecord = getIntent().getBooleanExtra("autoRecord", false);
        if (autoRecord && !autoRecordTriggered) {
            if (userId == null || userId.trim().isEmpty()) {
                Log.e(TAG, "自动录像失败：userId 为空");
                Toast.makeText(this, "缺少用户ID，无法自动录制", Toast.LENGTH_SHORT).show();
                return;
            }
            autoRecordTriggered = true;
            Intent intent = new Intent(this, VideoRecorderActivity.class);
            intent.putExtra("userId", userId);
            intent.putExtra("taskId", taskId);
            Log.d(TAG, "触发自动录像: userId=" + userId + ", taskId=" + taskId);
            startActivityForResult(intent, REQUEST_CODE_RECORD_VIDEO);
        }
    }

    protected void setupTabListeners() {
        TextView tabPendingUpload = findViewById(R.id.tabPendingUpload);
        TextView tabUploaded = findViewById(R.id.tabUploaded);
        if (tabPendingUpload != null) {
            tabPendingUpload.setOnClickListener(v -> switchTab("待上传"));
        }
        if (tabUploaded != null) {
            tabUploaded.setOnClickListener(v -> switchTab("已上传"));
        }
    }

    protected void switchTab(String tab) {
        currentTab = tab;
        updateTabUI();
        loadVideos(currentTab);
    }

    protected void updateTabUI() {
        TextView tabPendingUpload = findViewById(R.id.tabPendingUpload);
        TextView tabUploaded = findViewById(R.id.tabUploaded);
        if (tabPendingUpload != null) {
            tabPendingUpload.setTextColor(currentTab.equals("待上传") ? Color.parseColor("#FF6C00") : Color.BLACK);
        }
        if (tabUploaded != null) {
            tabUploaded.setTextColor(currentTab.equals("已上传") ? Color.parseColor("#FF6C00") : Color.BLACK);
        }
    }

    protected void loadVideos(String tab) {
        String statusQuery;
        if (tab.equals("待上传")) {
            statusQuery = "待上传,上传中,上传失败,已暂停";
        } else if (tab.equals("已上传")) {
            statusQuery = "已上传";
        } else {
            Log.w(TAG, "无效的标签页: " + tab);
            return;
        }

        Log.d(TAG, "加载视频，taskId=" + taskId + ", statusQuery=" + statusQuery);
        apiService.getVideoList(taskId, statusQuery).enqueue(new Callback<List<Video>>() {
            @Override
            public void onResponse(Call<List<Video>> call, Response<List<Video>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    Log.d(TAG, "加载视频成功，数据量: " + response.body().size());
                    if (videoAdapter != null) {
                        videoAdapter.updateVideos(response.body());
                        recyclerViewVideos.post(() -> videoAdapter.notifyDataSetChanged());
                    } else {
                        Log.e(TAG, "videoAdapter 为 null，无法更新视频列表");
                    }
                } else {
                    Log.e(TAG, "加载视频失败，HTTP 状态码: " + response.code() + ", 错误: " + response.message());
                }
            }

            @Override
            public void onFailure(Call<List<Video>> call, Throwable t) {
                Log.e(TAG, "加载视频失败，异常: " + t.getMessage(), t);
            }
        });
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent: new intent=" + intent.getData());
        setIntent(intent);

        long newTaskId = intent.getLongExtra("taskId", -1L);
        String newUserId = intent.getStringExtra("userId");
        Log.d(TAG, "onNewIntent: newTaskId=" + newTaskId + ", newUserId=" + newUserId);

        if (newUserId == null || newUserId.trim().isEmpty()) {
            Log.e(TAG, "onNewIntent: newUserId 为空，忽略更新");
            return;
        }

        if (newTaskId == -1L) {
            if (this instanceof UserVideoListActivity) {
                Log.d(TAG, "已在 UserVideoListActivity，更新 userId=" + newUserId);
                userId = newUserId;
                loadVideos(currentTab);
            } else {
                Log.d(TAG, "新 Intent 无 taskId，仅有 userId=" + newUserId + "，跳转到 UserVideoListActivity");
                Intent userIntent = new Intent(this, UserVideoListActivity.class);
                userIntent.putExtra("userId", newUserId);
                userIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                startActivity(userIntent);
                finish();
            }
            return;
        }

        taskId = newTaskId;
        userId = newUserId;
        loadVideos(currentTab);

        boolean autoRecord = intent.getBooleanExtra("autoRecord", false);
        if (autoRecord && !autoRecordTriggered) {
            if (userId == null || userId.trim().isEmpty()) {
                Log.e(TAG, "新 Intent 自动录像失败：userId 为空");
                Toast.makeText(this, "缺少用户ID，无法自动录制", Toast.LENGTH_SHORT).show();
                return;
            }
            autoRecordTriggered = true;
            Intent recordIntent = new Intent(this, VideoRecorderActivity.class);
            recordIntent.putExtra("userId", userId);
            recordIntent.putExtra("taskId", taskId);
            Log.d(TAG, "新 Intent 触发自动录像: userId=" + userId + ", taskId=" + taskId);
            startActivityForResult(recordIntent, REQUEST_CODE_RECORD_VIDEO);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d(TAG, "onActivityResult: requestCode=" + requestCode + ", resultCode=" + resultCode + ", local userId=" + userId);

        if (requestCode == REQUEST_CODE_RECORD_VIDEO && resultCode == RESULT_OK && data != null) {
            String videoFilePath = data.getStringExtra("videoFilePath");
            String resultUserId = data.getStringExtra("userId");
            long resultTaskId = data.getLongExtra("taskId", -1L);
            Log.d(TAG, "onActivityResult: videoFilePath=" + videoFilePath + ", resultUserId=" + resultUserId + ", resultTaskId=" + resultTaskId);

            if (videoFilePath != null) {
                if (resultUserId == null || resultUserId.trim().isEmpty()) {
                    Log.w(TAG, "onActivityResult: resultUserId 为 null，尝试使用本地 userId=" + userId);
                    resultUserId = userId;
                }
                if (resultUserId == null || resultUserId.trim().isEmpty()) {
                    Log.e(TAG, "onActivityResult: 所有 userId 均为 null，无法创建视频");
                    Toast.makeText(this, "用户ID缺失，无法保存视频", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (resultTaskId == -1L) {
                    Log.w(TAG, "onActivityResult: resultTaskId 无效，使用本地 taskId=" + taskId);
                    resultTaskId = taskId;
                }
                // 不再重复创建视频，因为VideoRecorderActivity中已经创建了
                // Video newVideo = new Video(videoFilePath, "待上传", resultTaskId);
                // newVideo.setInvalid(false);
                // newVideo.setRemoved(false);
                // newVideo.setUserId(resultUserId);
                // Log.d(TAG, "创建新视频: filePath=" + videoFilePath + ", userId=" + resultUserId + ", taskId=" + resultTaskId);
                // apiService.createVideo(newVideo).enqueue(new Callback<Video>() {
                //     @Override
                //     public void onResponse(Call<Video> call, Response<Video> response) {
                //         if (response.isSuccessful() && currentTab.equals("待上传")) {
                //             Log.d(TAG, "创建视频成功，重新加载视频列表");
                //             loadVideos(currentTab);
                //         } else {
                //             Log.e(TAG, "创建视频失败，HTTP 状态码: " + response.code() + ", 错误: " + response.message());
                //         }
                //     }
                //
                //     @Override
                //     public void onFailure(Call<Video> call, Throwable t) {
                //         Log.e(TAG, "创建视频失败，异常: " + t.getMessage(), t);
                //     }
                // });
                
                // 直接刷新视频列表
                Log.d(TAG, "视频已由VideoRecorderActivity创建，直接刷新列表");
                loadVideos(currentTab);
            } else {
                Log.w(TAG, "onActivityResult: videoFilePath 为 null");
            }
        } else {
            Log.w(TAG, "onActivityResult: 请求码不匹配或数据为空，requestCode=" + requestCode + ", resultCode=" + resultCode);
        }
        autoRecordTriggered = false;
        Log.d(TAG, "onActivityResult: 重置 autoRecordTriggered");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy 被调用，正在清理...");
        if (videoAdapter != null) {
            videoAdapter.onDestroy();
            videoAdapter = null;
            Log.d(TAG, "清理 videoAdapter");
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putString("currentTab", currentTab);
        outState.putString("userId", userId);
        outState.putLong("taskId", taskId);
        Log.d(TAG, "onSaveInstanceState: 保存 currentTab=" + currentTab + ", userId=" + userId + ", taskId=" + taskId);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        if (savedInstanceState != null) {
            currentTab = savedInstanceState.getString("currentTab", "待上传");
            userId = savedInstanceState.getString("userId");
            taskId = savedInstanceState.getLong("taskId", -1L);
            Log.d(TAG, "onRestoreInstanceState: 恢复 userId=" + userId + ", taskId=" + taskId);
            if (userId == null || userId.trim().isEmpty()) {
                Log.e(TAG, "onRestoreInstanceState: 恢复的 userId 为空，关闭活动");
                Toast.makeText(this, "缺少用户ID，无法继续", Toast.LENGTH_LONG).show();
                finish();
            } else {
                updateTabUI();
                loadVideos(currentTab);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "onPause 被调用...");
        // 不再在onPause中暂停上传，让UploadService在后台继续运行
        // if (videoAdapter != null) {
        //     videoAdapter.pauseAllUploads();
        // }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume 被调用，恢复所有上传...");
        // 恢复所有上传任务
        if (videoAdapter != null) {
            videoAdapter.resumeAllUploads();
        }
    }
}