package com.example.myapplication;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.hardware.Camera;
import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.media.MediaRecorder;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;
import android.widget.SeekBar;
import android.widget.RelativeLayout;
import android.net.Uri;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import com.example.myapplication.Video;
import com.example.myapplication.Config;

public class VideoRecorderActivity extends AppCompatActivity implements SurfaceHolder.Callback {

    private static final String TAG = "VideoRecorderActivity";
    private static final int REQUEST_PERMISSIONS_CODE = 1099;
    private static final int REQUEST_CODE_RECORD_VIDEO = 1;
    private SurfaceView surfaceView;
    private SurfaceHolder surfaceHolder;
    private MediaRecorder mediaRecorder;
    private Camera camera;
    private boolean isRecording = false;
    private boolean isPaused = false;
    private Button buttonSwitchCamera;
    private Button buttonStartRecording;
    private Button buttonPauseRecording;
    private TextView textViewRecordingTime;
    private int currentCameraId = Camera.CameraInfo.CAMERA_FACING_BACK;
    private String videoFilePath;
    private String finalVideoPath; // 最终合并后的视频路径
    private boolean isStoppedByButton = false;
    private long recordingStartTime;
    private long totalRecordingTime = 0; // 总录制时间（不包括暂停时间）
    private long currentSegmentStartTime = 0; // 当前片段开始时间
    private Handler handler = new Handler(Looper.getMainLooper());
    private Handler videoHandler = new Handler(Looper.getMainLooper());  // 用于视频进度更新
    private BatteryReceiver batteryReceiver;
    private String userId; // 新增 userId 字段
    private long taskId;   // 新增 taskId 字段

    // 分段录制相关
    private java.util.List<String> videoSegments = new java.util.ArrayList<>(); // 视频片段列表
    private int segmentCount = 0; // 片段计数

    // 视频播放相关
    private boolean isVideoPlaying = false;
    private VideoView videoView;  // 添加VideoView用于视频预览
    private RelativeLayout loadingOverlay;  // 加载遮罩层
    private LinearLayout videoControlPanel;  // 视频控制面板
    private SeekBar videoSeekbar;  // 视频进度条
    private Button buttonPlayPause;  // 播放/暂停按钮
    private Button buttonRestart;  // 重新开始按钮
    private Runnable updateVideoProgressRunnable = new Runnable() {
        @Override
        public void run() {
            if (videoView != null && isVideoPlaying) {
                int currentPosition = videoView.getCurrentPosition();
                int duration = videoView.getDuration();
                if (duration > 0) {
                    int progress = (int) (((float) currentPosition / duration) * 100);
                    videoSeekbar.setProgress(progress);
                }
                videoHandler.postDelayed(this, 1000);
            }
        }
    };


    // 离线待提交（无网时不返回，保留在录制页）
    private boolean pendingReturn = false;
    private String pendingReturnPath = null;

    // 网络监听（用于待提交时，网络恢复给予提示）
    private ConnectivityManager.NetworkCallback connectivityCallback;

    // 双击检测相关
    private long lastTouchTime = 0;
    private static final long DOUBLE_CLICK_TIME_DELTA = 300; // 双击间隔时间（毫秒）
    private int touchCount = 0;

    private Runnable updateRecordingTimeRunnable = new Runnable() {
        @Override
        public void run() {
            if (isRecording) {
                long currentTime;
                if (isPaused) {
                    // 暂停时显示总录制时间
                    currentTime = totalRecordingTime;
                } else {
                    // 录制时显示总时间 + 当前片段时间
                    long currentSegmentTime = System.currentTimeMillis() - currentSegmentStartTime;
                    currentTime = totalRecordingTime + currentSegmentTime;
                }
                textViewRecordingTime.setText(formatElapsedTime(currentTime));
                handler.postDelayed(this, 1000);
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setContentView(R.layout.activity_video_recorder);

        // 从 Intent 获取 userId 和 taskId
        userId = getIntent().getStringExtra("userId");
        taskId = getIntent().getLongExtra("taskId", -1L);
        Log.d(TAG, "onCreate: userId=" + userId + ", taskId=" + taskId);

        // 初始化视图组件
        surfaceView = findViewById(R.id.surface_view);
        videoView = findViewById(R.id.video_view);
        surfaceHolder = surfaceView.getHolder();
        surfaceHolder.addCallback(this);

        // 初始化视频控制组件
        loadingOverlay = findViewById(R.id.loading_overlay);
        videoControlPanel = findViewById(R.id.video_control_panel);
        videoSeekbar = findViewById(R.id.video_seekbar);
        buttonPlayPause = findViewById(R.id.button_play_pause);
        buttonRestart = findViewById(R.id.button_restart);
        buttonStartRecording = findViewById(R.id.button_start_recording);
        buttonPauseRecording = findViewById(R.id.button_pause_recording);
        buttonSwitchCamera = findViewById(R.id.button_switch_camera);
        textViewRecordingTime = findViewById(R.id.text_view_recording_time);

        // 设置视频控制监听器
        setupVideoControls();

        buttonStartRecording.setOnClickListener(v -> {
            // 若存在待提交的已完成视频（之前因无网未返回），优先尝试返回
            if (pendingReturn) {
                String path = pendingReturnPath != null ? pendingReturnPath : (finalVideoPath != null ? finalVideoPath : videoFilePath);
                if (path == null) {
                    Toast.makeText(this, "未找到可提交的视频", Toast.LENGTH_SHORT).show();
                    return;
                }

                if (hasInternetConnectivity()) {
                    // 创建视频对象并提交
                    Video pendingVideo = new Video(path, "待上传", taskId);
                    pendingVideo.setInvalid(false);
                    pendingVideo.setRemoved(false);
                    pendingVideo.setUserId(userId);

                    // 显示加载提示，禁用按钮防止重复操作
                    showLoadingOverlay(true, "正在提交视频，请稍候...");
                    Toast.makeText(this, "正在提交视频，请稍候...", Toast.LENGTH_SHORT).show();

                    submitVideoToServer(pendingVideo);
                } else {
                    Toast.makeText(this, "当前无网络，无法提交，请联网后再试", Toast.LENGTH_LONG).show();
                    // 显示视频预览
                    showVideoPreview(path);
                    videoControlPanel.setVisibility(View.VISIBLE);
                }
                return;
            }

            if (isRecording) {
                showDoubleClickToStopHint();
            } else {
                if (checkBatteryLevel()) {
                    startRecording();
                } else {
                    Toast.makeText(this, "电量低于 20%，无法开始录像", Toast.LENGTH_SHORT).show();
                }
            }
        });

        // 暂停/继续按钮
        buttonPauseRecording.setOnClickListener(v -> {
            if (isRecording) {
                if (isPaused) {
                    resumeRecording();
                } else {
                    pauseRecording();
                }
            }
        });

        surfaceView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                // 非录制状态下正常对焦
                if (!isRecording) {
                    handleFocus(event.getX(), event.getY());
                }
            }
            return true;
        });

        buttonSwitchCamera.setOnClickListener(v -> switchCamera());

        // 检查并请求权限
        if (!hasRequiredPermissions()) {
            ActivityCompat.requestPermissions(this,
                    new String[]{
                            Manifest.permission.CAMERA,
                            Manifest.permission.RECORD_AUDIO,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE
                    },
                    REQUEST_PERMISSIONS_CODE);
        } else {
            initializeCamera(currentCameraId);
        }

        // 注册电量广播接收器
        registerBatteryReceiver();

        updateSwitchCameraButtonState();
        
        // 如果有待提交的视频，显示预览界面并阻止自动录制
        if (pendingReturn && pendingReturnPath != null) {
            showVideoPreview(pendingReturnPath);
            videoControlPanel.setVisibility(View.VISIBLE);
            surfaceView.setVisibility(View.GONE);
            videoView.setVisibility(View.VISIBLE);
            
            // 如果是通过deeplink启动且需要自动录制，则阻止自动录制
            if (getIntent().getBooleanExtra("autoRecord", false)) {
                Toast.makeText(this, "有待提交的视频，无法自动开始新录制", Toast.LENGTH_LONG).show();
                // 移除自动录制标记，防止后续触发
                getIntent().removeExtra("autoRecord");
            }
        }
        
        // 若存在未完成的录制会话，自动恢复（仅在没有待提交视频时）
        if (!pendingReturn) {
            restoreRecordingSessionIfAny();
        }
    }

    /**
     * 显示双击停止录制提示
     */
    private void showDoubleClickToStopHint() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTouchTime < DOUBLE_CLICK_TIME_DELTA) {
            touchCount++;
            if (touchCount >= 2) {
                // 双击停止录制
                isStoppedByButton = true;
                stopRecording(true);
                touchCount = 0;
                return;
            }
        } else {
            touchCount = 1;
        }
        lastTouchTime = currentTime;

        // 显示提示信息
        if (touchCount == 1) {
            Toast.makeText(this, "双击按钮停止录制", Toast.LENGTH_SHORT).show();
        }
    }

    private void registerBatteryReceiver() {
        batteryReceiver = new BatteryReceiver();
        IntentFilter filter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        registerReceiver(batteryReceiver, filter);
        Log.d(TAG, "注册电量广播接收器");
    }

    private void unregisterBatteryReceiver() {
        if (batteryReceiver != null) {
            unregisterReceiver(batteryReceiver);
            batteryReceiver = null;
            Log.d(TAG, "取消注册电量广播接收器");
        }
    }

    private boolean checkBatteryLevel() {
        Intent batteryIntent = registerReceiver(null, new IntentFilter(Intent.ACTION_BATTERY_CHANGED));
        if (batteryIntent != null) {
            int level = batteryIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
            int scale = batteryIntent.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
            if (level != -1 && scale != -1) {
                float batteryPercentage = (level / (float) scale) * 100;
                Log.d(TAG, "当前电量: " + batteryPercentage + "%");
                return batteryPercentage >= 20; // 修改为20%
            }
        }
        return true; // 默认允许录像
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initializeCamera(currentCameraId);
            } else {
                Toast.makeText(this, "需要相机、录音和存储权限", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    private void handleFocus(float x, float y) {
        if (camera == null) return;

        try {
            Camera.Parameters params = camera.getParameters();
            if (params == null || params.getMaxNumFocusAreas() <= 0) {
                return;
            }
            int surfaceWidth = surfaceView.getWidth();
            int surfaceHeight = surfaceView.getHeight();

            if (surfaceWidth == 0 || surfaceHeight == 0) {
                Log.e(TAG, "SurfaceView width or height is 0");
                return;
            }

            int focusX = (int) ((x / surfaceWidth) * 2000 - 1000);
            int focusY = (int) ((y / surfaceHeight) * 2000 - 1000);
            focusX = Math.max(-1000, Math.min(1000, focusX));
            focusY = Math.max(-1000, Math.min(1000, focusY));

            Camera.Area focusArea = new Camera.Area(new android.graphics.Rect(
                    focusX - 100, focusY - 100, focusX + 100, focusY + 100), 1000);
            params.setFocusAreas(Collections.singletonList(focusArea));

            try {
                camera.setParameters(params);
                camera.autoFocus((success, camera) -> {});
            } catch (RuntimeException e) {
                Log.e(TAG, "自动对焦失败: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理对焦失败: " + e.getMessage(), e);
        }
    }

    private void initializeCamera(int cameraId) {
        if (camera != null) {
            camera.release();
            camera = null;
        }
        try {
            camera = Camera.open(cameraId);
            if (camera == null) {
                Log.e(TAG, "无法打开摄像头 ID: " + cameraId);
                Toast.makeText(this, "无法打开摄像头", Toast.LENGTH_SHORT).show();
                return;
            }
            camera.setDisplayOrientation(90);

            // 如果 SurfaceView 还没准备好，等待一下
            if (surfaceView.getWidth() == 0 || surfaceView.getHeight() == 0) {
                surfaceView.post(() -> setupCameraPreview());
            } else {
                setupCameraPreview();
            }
        } catch (RuntimeException e) {
            Log.e(TAG, "初始化摄像头失败: " + e.getMessage(), e);
            Toast.makeText(this, "初始化摄像头失败", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    private void setupCameraPreview() {
        if (camera == null || surfaceHolder == null) {
            Log.e(TAG, "Camera or SurfaceHolder is null");
            return;
        }

        try {
            int surfaceWidth = surfaceView.getWidth();
            int surfaceHeight = surfaceView.getHeight();

            if (surfaceWidth == 0 || surfaceHeight == 0) {
                Log.e(TAG, "SurfaceView width or height is 0");
                return;
            }

            float screenRatio = (float) surfaceHeight / surfaceWidth;
            Log.d(TAG, "SurfaceView width: " + surfaceWidth + ", height: " + surfaceHeight + ", ratio: " + screenRatio);

            Camera.Parameters params = camera.getParameters();
            if (params == null) {
                Log.e(TAG, "无法获取Camera参数");
                return;
            }

            Camera.Size previewSize = getOptimalPreviewSize(params.getSupportedPreviewSizes(), surfaceWidth, surfaceHeight);
            if (previewSize == null) {
                Log.e(TAG, "无法获取合适的预览尺寸");
                return;
            }
            params.setPreviewSize(previewSize.width, previewSize.height);

            if (params.getSupportedFocusModes().contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO)) {
                params.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);
            }

            camera.setParameters(params);
            camera.setPreviewDisplay(surfaceHolder);
            camera.startPreview();
            Log.d(TAG, "Camera preview setup completed");
        } catch (IOException | RuntimeException e) {
            Log.e(TAG, "设置预览失败: " + e.getMessage(), e);
        }
    }

    private Camera.Size getOptimalPreviewSize(List<Camera.Size> sizes, int targetWidth, int targetHeight) {
        final double ASPECT_TOLERANCE = 0.1;
        double targetRatio = (double) targetWidth / targetHeight;

        if (sizes == null || sizes.isEmpty()) {
            Log.e(TAG, "No preview sizes available");
            return null;
        }

        Camera.Size optimalSize = null;
        double minDiff = Double.MAX_VALUE;

        for (Camera.Size size : sizes) {
            double ratio = (double) size.width / size.height;
            double rotatedRatio = (double) size.height / size.width;

            if (Math.abs(rotatedRatio - targetRatio) <= ASPECT_TOLERANCE) {
                double diff = Math.abs(size.height - targetWidth);
                if (diff < minDiff) {
                    optimalSize = size;
                    minDiff = diff;
                }
            } else if (Math.abs(ratio - targetRatio) <= ASPECT_TOLERANCE) {
                double diff = Math.abs(size.height - targetHeight);
                if (diff < minDiff) {
                    optimalSize = size;
                    minDiff = diff;
                }
            }
        }

        if (optimalSize == null) {
            Log.w(TAG, "No size matches target aspect ratio, selecting closest resolution");
            minDiff = Double.MAX_VALUE;
            for (Camera.Size size : sizes) {
                int areaDiff = Math.abs((size.width * size.height) - (targetWidth * targetHeight));
                if (areaDiff < minDiff) {
                    optimalSize = size;
                    minDiff = areaDiff;
                }
            }
        }

        if (optimalSize != null) {
            Log.d(TAG, "Selected preview size: " + optimalSize.width + "x" + optimalSize.height);
        }
        return optimalSize;
    }

    private void startRecording() {
        // 如果有待提交的视频，不允许开始新的录制
        if (pendingReturn) {
            Toast.makeText(this, "有待提交的视频，请提交后再开始新的录制", Toast.LENGTH_LONG).show();
            // 显示视频预览
            if (pendingReturnPath != null) {
                showVideoPreview(pendingReturnPath);
                videoControlPanel.setVisibility(View.VISIBLE);
            }
            return;
        }
        
        // 检查相机是否已准备好
        if (camera == null) {
            Toast.makeText(this, "相机未准备好，请稍后重试", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // 重置录制状态
            isRecording = true;
            isPaused = false;
            isStoppedByButton = false;
            currentSegmentStartTime = System.currentTimeMillis();
            videoFilePath = getExternalFilesDir(Environment.DIRECTORY_MOVIES) + "/video_" + System.currentTimeMillis() + ".mp4";

            // 准备并启动录制
            if (prepareRecorder()) {
                mediaRecorder.start();
                buttonStartRecording.setText("停止录制");
                buttonPauseRecording.setVisibility(View.VISIBLE);
                buttonPauseRecording.setText("暂停");
                updateSwitchCameraButtonState(); // 更新切换摄像头按钮状态
                handler.post(updateRecordingTimeRunnable);
                Log.d(TAG, "开始录制: " + videoFilePath);
            } else {
                isRecording = false;
                Toast.makeText(this, "录制准备失败", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            isRecording = false;
            Log.e(TAG, "开始录制失败", e);
            Toast.makeText(this, "开始录制失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void stopRecording(boolean returnResult) {
        if (isRecording) {
            // 计算总录制时间（在重置变量之前）
            long finalTotalTime = totalRecordingTime;
            if (!isPaused) {
                // 如果不是暂停状态，加上当前片段的时间
                finalTotalTime += System.currentTimeMillis() - currentSegmentStartTime;
            }

            try {
                if (!isPaused) {
                    // 如果不是暂停状态，停止当前录制
                    // 检查mediaRecorder状态，确保它正在录制
                    if (mediaRecorder != null) {
                        mediaRecorder.stop();
                        // 保存最后一个片段
                        videoSegments.add(videoFilePath);
                        Log.d(TAG, "停止录制，最后片段保存到: " + videoFilePath);
                    }
                } else {
                    Log.d(TAG, "从暂停状态停止录制");
                }

                // 合并所有片段
                mergeVideoSegments();

            } catch (RuntimeException e) {
                Log.e(TAG, "停止录制失败: " + e.getMessage(), e);
                if (videoFilePath != null) {
                    File file = new File(videoFilePath);
                    if (file.exists()) {
                        file.delete();
                        Log.d(TAG, "删除无效视频文件: " + videoFilePath);
                    }
                }
            } finally {
                releaseMediaRecorder();
                isRecording = false;
                isPaused = false;
                totalRecordingTime = 0;
                segmentCount = 0;
                buttonStartRecording.setText("开始录制");
                buttonPauseRecording.setVisibility(View.GONE);
                updateSwitchCameraButtonState();
                handler.removeCallbacks(updateRecordingTimeRunnable);
                textViewRecordingTime.setText("");

                Log.d(TAG, "总录制时间: " + finalTotalTime + "ms");

                if (finalTotalTime > 1000) {
                    // 录制时间超过1秒，保存视频
                    if (returnResult && isStoppedByButton) {
                        // 手动停止：若无网络则留在本页待提交；有网络则返回结果
                        String path = finalVideoPath != null ? finalVideoPath : videoFilePath;
                        if (!hasInternetConnectivity()) {
                            pendingReturn = true;
                            pendingReturnPath = path;
                            buttonStartRecording.setText("提交");
                            Toast.makeText(this, "当前无网络，已保留在录制界面，请联网后点击‘提交’返回", Toast.LENGTH_LONG).show();

                            // 显示视频预览
                            showVideoPreview(path);
                        } else {
                            // 直接提交视频到服务器，等待响应后再返回
                            Video newVideo = new Video(path, "待上传", taskId);
                            newVideo.setInvalid(false);
                            newVideo.setRemoved(false);
                            newVideo.setUserId(userId);

                            submitVideoAndReturn(newVideo);
                        }
                    } else {
                        // 非手动停止（如低电量、来电等），也保存视频
                        Log.d(TAG, "非手动停止，视频已保存: " + (finalVideoPath != null ? finalVideoPath : videoFilePath));
                        Toast.makeText(this, "录制已保存", Toast.LENGTH_SHORT).show();
                        // 完成保存，清理会话
                        clearRecordingSession();
                        finish();
                    }
                } else {
                    // 录制时间不足1秒，删除视频文件
                    Log.d(TAG, "录制时间不足 1 秒，删除视频文件");
                    Toast.makeText(this, "录制视频时长不足1秒，已删除", Toast.LENGTH_SHORT).show();

                    // 删除所有片段文件
                    for (String segmentPath : videoSegments) {
                        File file = new File(segmentPath);
                        if (file.exists()) {
                            file.delete();
                            Log.d(TAG, "删除片段文件: " + segmentPath);
                        }
                    }
                    if (videoFilePath != null) {
                        File file = new File(videoFilePath);
                        if (file.exists()) {
                            file.delete();
                            Log.d(TAG, "删除不足 1 秒的视频文件: " + videoFilePath);
                        }
                    }
                    finish();
                }
            }
        } else {
            Log.w(TAG, "未在录制状态，忽略停止请求");
            finish();
        }
    }

    /**
     * 提交视频到服务器并等待响应后再返回
     * @param video 视频对象
     */
    private void submitVideoAndReturn(Video video) {
        // 显示加载提示，禁用按钮防止重复操作
        showLoadingOverlay(true, "正在提交视频，请稍候...");
        buttonStartRecording.setEnabled(false);
        buttonPauseRecording.setEnabled(false);
        buttonSwitchCamera.setEnabled(false);
        Toast.makeText(this, "正在提交视频，请稍候...", Toast.LENGTH_SHORT).show();

        // 创建Gson实例，用于处理日期格式
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(Config.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        ApiService apiService = retrofit.create(ApiService.class);

        Log.d(TAG, "开始提交视频到服务器: filePath=" + video.getFilePath());

        apiService.createVideo(video).enqueue(new Callback<Video>() {
            @Override
            public void onResponse(Call<Video> call, Response<Video> response) {
                // 隐藏加载遮罩层
                showLoadingOverlay(false, null);

                // 恢复按钮状态
                buttonStartRecording.setEnabled(true);
                buttonPauseRecording.setEnabled(true);
                buttonSwitchCamera.setEnabled(true);

                if (response.isSuccessful()) {
                    Log.d(TAG, "视频提交成功");
                    Toast.makeText(VideoRecorderActivity.this, "视频提交成功", Toast.LENGTH_SHORT).show();

                    // 提交成功后，清理录制会话并返回结果
                    clearRecordingSession();

                    Intent resultIntent = new Intent();
                    resultIntent.putExtra("videoFilePath", video.getFilePath());
                    resultIntent.putExtra("userId", userId);
                    resultIntent.putExtra("taskId", taskId);
                    setResult(RESULT_OK, resultIntent);
                    finish();
                } else {
                    Log.e(TAG, "视频提交失败，HTTP 状态码: " + response.code() + ", 错误: " + response.message());
                    Toast.makeText(VideoRecorderActivity.this, "视频提交失败，请检查网络连接后重试", Toast.LENGTH_LONG).show();

                    // 保存视频信息到本地，供后续重试
                    pendingReturn = true;
                    pendingReturnPath = video.getFilePath();
                    buttonStartRecording.setText("提交");
                    buttonStartRecording.setEnabled(true);

                    // 显示视频预览并确保控制面板可见
                    showVideoPreview(video.getFilePath());
                    videoControlPanel.setVisibility(View.VISIBLE);

                    Toast.makeText(VideoRecorderActivity.this, "视频已保存，点击'提交'按钮可重新提交", Toast.LENGTH_LONG).show();
                    Log.d(TAG, "视频提交失败，已保存视频文件: " + video.getFilePath());
                }
            }

            @Override
            public void onFailure(Call<Video> call, Throwable t) {
                Log.e(TAG, "视频提交失败，异常: " + t.getMessage(), t);

                // 隐藏加载遮罩层
                showLoadingOverlay(false, null);

                // 恢复按钮状态
                buttonStartRecording.setEnabled(true);
                buttonPauseRecording.setEnabled(true);
                buttonSwitchCamera.setEnabled(true);

                Toast.makeText(VideoRecorderActivity.this, "视频提交失败，请检查网络连接后重试", Toast.LENGTH_LONG).show();

                // 保存视频信息到本地，供后续重试
                pendingReturn = true;
                pendingReturnPath = video.getFilePath();
                buttonStartRecording.setText("提交");
                buttonStartRecording.setEnabled(true);

                Toast.makeText(VideoRecorderActivity.this, "视频已保存，点击'提交'按钮可重新提交", Toast.LENGTH_LONG).show();
                Log.d(TAG, "视频提交失败，已保存视频文件: " + video.getFilePath());
            }
        });
    }

    private boolean prepareRecorder() {
        if (camera == null) {
            Log.e(TAG, "Camera is null");
            return false;
        }
        camera.unlock();
        mediaRecorder = new MediaRecorder();
        mediaRecorder.setCamera(camera);

        mediaRecorder.setAudioSource(MediaRecorder.AudioSource.CAMCORDER);
        mediaRecorder.setVideoSource(MediaRecorder.VideoSource.CAMERA);
        mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);
        mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
        mediaRecorder.setVideoEncoder(MediaRecorder.VideoEncoder.H264);
        mediaRecorder.setVideoEncodingBitRate(1 * 1024 * 1024);
        mediaRecorder.setVideoFrameRate(15);
        mediaRecorder.setVideoSize(1920, 1080);

        // 为每个片段生成唯一的文件名
        videoFilePath = getExternalFilesDir(Environment.DIRECTORY_MOVIES) + "/segment_" + segmentCount + "_" + System.currentTimeMillis() + ".mp4";
        mediaRecorder.setOutputFile(videoFilePath);
        mediaRecorder.setPreviewDisplay(surfaceHolder.getSurface());

        Camera.CameraInfo info = new Camera.CameraInfo();
        Camera.getCameraInfo(currentCameraId, info);
        int rotation = getWindowManager().getDefaultDisplay().getRotation();
        int degrees = 0;
        switch (rotation) {
            case Surface.ROTATION_0: degrees = 0; break;
            case Surface.ROTATION_90: degrees = 90; break;
            case Surface.ROTATION_180: degrees = 180; break;
            case Surface.ROTATION_270: degrees = 270; break;
        }
        int result = (info.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) ?
                (info.orientation + degrees) % 360 :
                (info.orientation - degrees + 360) % 360;
        mediaRecorder.setOrientationHint(result);

        try {
            mediaRecorder.prepare();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "MediaRecorder 准备失败: " + e.getMessage(), e);
            releaseMediaRecorder();
            return false;
        }
    }

    private void releaseMediaRecorder() {
        if (mediaRecorder != null) {
            mediaRecorder.reset();
            mediaRecorder.release();
            mediaRecorder = null;
            Log.d(TAG, "释放 MediaRecorder");
        }
        if (camera != null) {
            try {
                camera.lock();
                camera.reconnect();
            } catch (IOException e) {
                Log.e(TAG, "Camera reconnect 失败: " + e.getMessage(), e);
            }
        }
    }

    private void switchCamera() {
        if (Camera.getNumberOfCameras() > 1 && !isRecording) {
            camera.stopPreview();
            camera.release();
            currentCameraId = (currentCameraId == Camera.CameraInfo.CAMERA_FACING_BACK) ?
                    Camera.CameraInfo.CAMERA_FACING_FRONT : Camera.CameraInfo.CAMERA_FACING_BACK;
            initializeCamera(currentCameraId);
            Log.d(TAG, "切换摄像头到: " + (currentCameraId == Camera.CameraInfo.CAMERA_FACING_BACK ? "后置" : "前置"));
        }
    }

    /**
     * 暂停录制 - 停止当前片段
     */
    private void pauseRecording() {
        if (isRecording && !isPaused) {
            try {
                // 检查mediaRecorder状态，确保它正在录制
                if (mediaRecorder != null) {
                    mediaRecorder.stop();
                    releaseMediaRecorder();

                    // 保存当前片段
                    videoSegments.add(videoFilePath);

                    // 更新总录制时间
                    long currentSegmentTime = System.currentTimeMillis() - currentSegmentStartTime;
                    totalRecordingTime += currentSegmentTime;

                    isPaused = true;
                    buttonPauseRecording.setText("继续");
                    Toast.makeText(this, "录制已暂停", Toast.LENGTH_SHORT).show();
                    Log.d(TAG, "录制暂停，片段 " + segmentCount + " 已保存: " + videoFilePath);

                    segmentCount++;
                }
            } catch (Exception e) {
                Log.e(TAG, "暂停录制失败: " + e.getMessage(), e);
                Toast.makeText(this, "暂停失败", Toast.LENGTH_SHORT).show();

                // 即使暂停失败，也要更新状态以防止进一步错误
                isPaused = true;
                buttonPauseRecording.setText("继续");
            }
        }
    }

    /**
     * 继续录制 - 开始新片段
     */
    private void resumeRecording() {
        if (isRecording && isPaused) {
            try {
                // 如果相机为null，需要重新初始化
                if (camera == null) {
                    Log.d(TAG, "Camera is null, reinitializing...");
                    initializeCamera(currentCameraId);
                    // 等待相机初始化完成
                    if (camera == null) {
                        Log.e(TAG, "Failed to reinitialize camera");
                        Toast.makeText(this, "相机初始化失败，无法继续录制", Toast.LENGTH_SHORT).show();
                        return;
                    }
                }

                // 确保 SurfaceHolder 可用
                if (surfaceHolder == null || !surfaceHolder.getSurface().isValid()) {
                    Log.e(TAG, "SurfaceHolder is not valid");
                    Toast.makeText(this, "预览界面异常，无法继续录制", Toast.LENGTH_SHORT).show();
                    return;
                }

                // 准备新的录制片段
                if (prepareRecorder()) {
                    mediaRecorder.start();
                    currentSegmentStartTime = System.currentTimeMillis();
                    isPaused = false;
                    buttonPauseRecording.setText("暂停");
                    Toast.makeText(this, "录制已继续", Toast.LENGTH_SHORT).show();
                    Log.d(TAG, "录制继续，开始片段 " + segmentCount);
                } else {
                    Toast.makeText(this, "继续录制失败", Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                Log.e(TAG, "继续录制失败: " + e.getMessage(), e);
                Toast.makeText(this, "继续失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 生成最终视频文件路径
     */
    private String generateFinalVideoPath() {
        return getExternalFilesDir(Environment.DIRECTORY_MOVIES) + "/video_" + System.currentTimeMillis() + ".mp4";
    }

    /**
     * 合并视频片段
     */
    private void mergeVideoSegments() {
        if (videoSegments.size() == 0) {
            Log.w(TAG, "没有视频片段需要合并");
            return;
        } else if (videoSegments.size() == 1) {
            // 只有一个片段，直接重命名
            File sourceFile = new File(videoSegments.get(0));
            File targetFile = new File(finalVideoPath);
            if (sourceFile.renameTo(targetFile)) {
                Log.d(TAG, "单片段视频重命名成功: " + finalVideoPath);
            } else {
                Log.e(TAG, "单片段视频重命名失败");
                finalVideoPath = videoSegments.get(0); // 使用原文件路径
            }
        } else {
            // 多个片段：使用 FFmpeg concat 无重编码合并
            Log.d(TAG, "开始使用FFmpeg合并 " + videoSegments.size() + " 个片段");
            try {
                if (finalVideoPath == null) finalVideoPath = generateFinalVideoPath();
                File listFile = new File(getExternalFilesDir(Environment.DIRECTORY_MOVIES), "concat_list.txt");
                try (java.io.FileWriter fw = new java.io.FileWriter(listFile)) {
                    for (String p : videoSegments) {
                        fw.write("file '" + p.replace("'", "\\'") + "'\n");
                    }
                }
                String cmd = "-f concat -safe 0 -i \"" + listFile.getAbsolutePath() + "\" -c copy -movflags +faststart \"" + finalVideoPath + "\"";
                Log.d(TAG, "FFmpeg 命令: " + cmd);
                com.arthenica.ffmpegkit.FFmpegSession session = com.arthenica.ffmpegkit.FFmpegKit.execute(cmd);
                if (com.arthenica.ffmpegkit.ReturnCode.isSuccess(session.getReturnCode())) {
                    Log.d(TAG, "FFmpeg 合并成功: " + finalVideoPath);
                    // 合并成功，删除临时片段
                    for (String seg : videoSegments) {
                        try { if (!seg.equals(finalVideoPath)) new File(seg).delete(); } catch (Exception ignore) {}
                    }
                } else {
                    Log.e(TAG, "FFmpeg 合并失败，尝试重编码方式。日志: " + session.getAllLogsAsString());
                    // 回退：重编码合并，更稳但耗时
                    String reencodeCmd = "-f concat -safe 0 -i \"" + listFile.getAbsolutePath() + "\" -c:v libx264 -c:a aac -movflags +faststart \"" + finalVideoPath + "\"";
                    com.arthenica.ffmpegkit.FFmpegSession s2 = com.arthenica.ffmpegkit.FFmpegKit.execute(reencodeCmd);
                    if (com.arthenica.ffmpegkit.ReturnCode.isSuccess(s2.getReturnCode())) {
                        Log.d(TAG, "FFmpeg 重编码合并成功: " + finalVideoPath);
                        for (String seg : videoSegments) { try { if (!seg.equals(finalVideoPath)) new File(seg).delete(); } catch (Exception ignore) {} }
                    } else {
                        Log.e(TAG, "FFmpeg 重编码合并仍失败: " + s2.getAllLogsAsString());
                        // 最坏情况：保留第一段作为结果，避免丢数据
                        File sourceFile = new File(videoSegments.get(0));
                        File targetFile = new File(finalVideoPath);
                        if (!sourceFile.equals(targetFile)) {
                            try {
                                java.io.FileInputStream in = new java.io.FileInputStream(sourceFile);
                                java.io.FileOutputStream out = new java.io.FileOutputStream(targetFile);
                                byte[] buf = new byte[8192];
                                int len;
                                while ((len = in.read(buf)) > 0) out.write(buf, 0, len);
                                in.close(); out.flush(); out.close();
                            } catch (Exception copyEx) { Log.e(TAG, "回退复制失败: " + copyEx.getMessage()); }
                        }
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "合并过程异常: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 找到最长的视频片段
     */
    private String findLongestSegment() {
        if (videoSegments.isEmpty()) {
            return null;
        }

        String longestSegment = videoSegments.get(0);
        long maxSize = 0;

        for (String segmentPath : videoSegments) {
            File file = new File(segmentPath);
            if (file.exists() && file.length() > maxSize) {
                maxSize = file.length();
                longestSegment = segmentPath;
            }
        }

        Log.d(TAG, "最长片段: " + longestSegment + " (大小: " + maxSize + " bytes)");
        return longestSegment;
    }

    /**
     * 保存当前录制会话到本地
     */
    private void persistRecordingSession() {
        try {
            RecordingSessionManager.Session s = new RecordingSessionManager.Session();
            s.active = true;
            s.segments = new java.util.ArrayList<>(videoSegments);
            s.totalMs = totalRecordingTime;
            s.segmentCount = segmentCount;
            s.finalPath = finalVideoPath;
            s.userId = userId;
            s.taskId = taskId;
            RecordingSessionManager.save(this, s);
            Log.d(TAG, "已保存录制会话: segments=" + s.segments.size() + ", totalMs=" + s.totalMs);
        } catch (Exception e) {
            Log.e(TAG, "保存录制会话失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理已完成的录制会话
     */
    private void clearRecordingSession() {
        RecordingSessionManager.clear(this);
        Log.d(TAG, "已清理录制会话");
    }

    /**
     * 如果存在未完成的会话，恢复并自动继续录制
     */
    private void restoreRecordingSessionIfAny() {
        // 如果已有待提交的视频，不需要恢复录制会话
        if (pendingReturn) {
            Log.d(TAG, "有待提交的视频，跳过录制会话恢复");
            return;
        }
        
        try {
            RecordingSessionManager.Session s = RecordingSessionManager.load(this);
            if (s.active && s.segments != null && !s.segments.isEmpty()) {
                // 恢复字段
                this.videoSegments.clear();
                this.videoSegments.addAll(s.segments);
                this.segmentCount = s.segmentCount;
                this.totalRecordingTime = s.totalMs;
                this.finalVideoPath = s.finalPath;
                if (this.userId == null) this.userId = s.userId;
                if (this.taskId <= 0) this.taskId = s.taskId;

                // 校验片段文件存在性
                java.util.Iterator<String> it = this.videoSegments.iterator();
                while (it.hasNext()) {
                    java.io.File f = new java.io.File(it.next());
                    if (!f.exists()) it.remove();
                }
                this.segmentCount = this.videoSegments.size();

                // 更新UI为暂停状态，准备继续
                this.isRecording = true;
                this.isPaused = true;
                buttonStartRecording.setText("停止录制");
                buttonPauseRecording.setVisibility(View.VISIBLE);
                buttonPauseRecording.setText("继续");
                textViewRecordingTime.setText(formatElapsedTime(totalRecordingTime));

                Toast.makeText(this, "已恢复上次未完成的录制，可继续", Toast.LENGTH_LONG).show();

                // 稍后自动继续（确保相机预览已就绪且电量允许）
                handler.postDelayed(() -> {
                    if (checkBatteryLevel()) {
                        resumeRecording();
                    } else {
                        Toast.makeText(this, "电量过低，无法继续录像", Toast.LENGTH_LONG).show();
                    }
                }, 600);
            }
        } catch (Exception e) {
            Log.e(TAG, "恢复录制会话失败: " + e.getMessage(), e);
        }
    }
    /**
     * 使用MediaMuxer合并视频片段（暂时不使用，太复杂）
     */
    private void mergeVideoSegmentsWithMediaMuxer() throws Exception {
        android.media.MediaMuxer muxer = new android.media.MediaMuxer(finalVideoPath, android.media.MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);

        int videoTrackIndex = -1;
        int audioTrackIndex = -1;
        boolean hasAudio = false;

        // 处理第一个片段，获取轨道信息
        android.media.MediaExtractor firstExtractor = new android.media.MediaExtractor();
        firstExtractor.setDataSource(videoSegments.get(0));

        for (int i = 0; i < firstExtractor.getTrackCount(); i++) {
            android.media.MediaFormat format = firstExtractor.getTrackFormat(i);
            String mime = format.getString(android.media.MediaFormat.KEY_MIME);

            if (mime.startsWith("video/")) {
                videoTrackIndex = muxer.addTrack(format);
                Log.d(TAG, "添加视频轨道: " + videoTrackIndex);
            } else if (mime.startsWith("audio/")) {
                audioTrackIndex = muxer.addTrack(format);
                hasAudio = true;
                Log.d(TAG, "添加音频轨道: " + audioTrackIndex);
            }
        }
        firstExtractor.release();

        muxer.start();

        // 合并所有片段
        for (String segmentPath : videoSegments) {
            Log.d(TAG, "合并片段: " + segmentPath);
            mergeSegment(muxer, segmentPath, videoTrackIndex, audioTrackIndex, hasAudio);
        }

        muxer.stop();
        muxer.release();
    }

    /**
     * 合并单个片段到Muxer
     */
    private void mergeSegment(android.media.MediaMuxer muxer, String segmentPath, int videoTrackIndex, int audioTrackIndex, boolean hasAudio) throws Exception {
        android.media.MediaExtractor extractor = new android.media.MediaExtractor();
        extractor.setDataSource(segmentPath);

        // 处理视频轨道
        for (int i = 0; i < extractor.getTrackCount(); i++) {
            android.media.MediaFormat format = extractor.getTrackFormat(i);
            String mime = format.getString(android.media.MediaFormat.KEY_MIME);

            if (mime.startsWith("video/")) {
                extractor.selectTrack(i);
                copyTrack(extractor, muxer, videoTrackIndex);
                extractor.unselectTrack(i);
            } else if (mime.startsWith("audio/") && hasAudio) {
                extractor.selectTrack(i);
                copyTrack(extractor, muxer, audioTrackIndex);
                extractor.unselectTrack(i);
            }
        }

        extractor.release();
    }

    /**
     * 复制轨道数据
     */
    private void copyTrack(android.media.MediaExtractor extractor, android.media.MediaMuxer muxer, int trackIndex) {
        java.nio.ByteBuffer buffer = java.nio.ByteBuffer.allocate(1024 * 1024); // 1MB buffer
        android.media.MediaCodec.BufferInfo bufferInfo = new android.media.MediaCodec.BufferInfo();

        while (true) {
            int sampleSize = extractor.readSampleData(buffer, 0);
            if (sampleSize < 0) {
                break;
            }

            bufferInfo.offset = 0;
            bufferInfo.size = sampleSize;
            bufferInfo.presentationTimeUs = extractor.getSampleTime();
            bufferInfo.flags = mapSampleFlagsToBufferInfoFlags(extractor.getSampleFlags());

            muxer.writeSampleData(trackIndex, buffer, bufferInfo);
            extractor.advance();
        }
    }
    private static int mapSampleFlagsToBufferInfoFlags(int sampleFlags) {
        int flags = 0;
        try {
            if ((sampleFlags & android.media.MediaExtractor.SAMPLE_FLAG_SYNC) != 0) {
                // 同步/关键帧
                flags |= android.media.MediaCodec.BUFFER_FLAG_KEY_FRAME;
                // 可选：新 API 也支持 SYNC_FRAME，但 KEY_FRAME 兼容性更好
            }
            if ((sampleFlags & android.media.MediaExtractor.SAMPLE_FLAG_PARTIAL_FRAME) != 0) {
                if (android.os.Build.VERSION.SDK_INT >= 26) {
                    flags |= android.media.MediaCodec.BUFFER_FLAG_PARTIAL_FRAME;
                }
            }
            // SAMPLE_FLAG_ENCRYPTED 不传递给 muxer 的 BufferInfo.flags
        } catch (Throwable ignore) {}
        return flags;
    }

    /**
     * 设置视频控制监听器
     */
    private void setupVideoControls() {
        // 视频进度条监听器
        videoSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && videoView != null) {
                    int duration = videoView.getDuration();
                    if (duration > 0) {
                        int position = (int) ((progress / 100.0) * duration);
                        videoView.seekTo(position);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // 暂停进度更新
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // 恢复进度更新
            }
        });

        // 播放/暂停按钮监听器
        buttonPlayPause.setOnClickListener(v -> {
            if (videoView != null) {
                if (isVideoPlaying) {
                    videoView.pause();
                    buttonPlayPause.setText("播放");
                    isVideoPlaying = false;
                    videoHandler.removeCallbacks(updateVideoProgressRunnable);
                } else {
                    videoView.start();
                    buttonPlayPause.setText("暂停");
                    isVideoPlaying = true;
                    videoHandler.post(updateVideoProgressRunnable);
                }
            }
        });

        // 重新开始按钮监听器
        buttonRestart.setOnClickListener(v -> {
            if (videoView != null) {
                videoView.seekTo(0);
                if (!isVideoPlaying) {
                    videoView.start();
                    buttonPlayPause.setText("暂停");
                    isVideoPlaying = true;
                    videoHandler.post(updateVideoProgressRunnable);
                }
            }
        });

        // 视频播放完成监听器
        videoView.setOnCompletionListener(mp -> {
            buttonPlayPause.setText("播放");
            isVideoPlaying = false;
            videoHandler.removeCallbacks(updateVideoProgressRunnable);
            videoSeekbar.setProgress(0);
        });
    }

    private void updateSwitchCameraButtonState() {
        buttonSwitchCamera.setEnabled(Camera.getNumberOfCameras() > 1 && !isRecording);
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (camera != null) {
            try {
                camera.setPreviewDisplay(holder);
                camera.startPreview();
                Log.d(TAG, "Surface 创建，启动预览");
            } catch (IOException e) {
                Log.e(TAG, "设置预览失败: " + e.getMessage(), e);
            }
        }
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        if (surfaceHolder.getSurface() == null || camera == null) return;
        try {
            camera.stopPreview();
        } catch (Exception e) {
            Log.e(TAG, "停止预览失败: " + e.getMessage(), e);
        }
        initializeCamera(currentCameraId);
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        if (camera != null) {
            camera.stopPreview();
            camera.release();
            camera = null;
            Log.d(TAG, "Surface 销毁，释放 Camera");
        }
    }

    private String formatElapsedTime(long elapsedTime) {
        int hours = (int) (elapsedTime / 3600000);
        int minutes = (int) (elapsedTime % 3600000) / 60000;
        int seconds = (int) ((elapsedTime % 60000) / 1000);
        return String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds);
    }

    private boolean hasRequiredPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }


    // 网络可用性检测（用于待提交判断）
    private boolean hasInternetConnectivity() {
        try {
            ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) return false;
            if (Build.VERSION.SDK_INT >= 23) {
                Network network = cm.getActiveNetwork();
                if (network == null) return false;
                NetworkCapabilities caps = cm.getNetworkCapabilities(network);
                return caps != null && caps.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
            } else {
                NetworkInfo info = cm.getActiveNetworkInfo();
                return info != null && info.isConnected();
            }
        } catch (Exception e) {
            Log.e(TAG, "Network check failed", e);
            return false;
        }
    }

    // 网络监听注册（API 24+）
    private void registerConnectivityCallback() {
        if (Build.VERSION.SDK_INT < 24) return;
        try {
            ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm == null) return;
            if (connectivityCallback != null) return;
            connectivityCallback = new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(Network network) {
                    if (pendingReturn) {
                        handler.post(() -> {
                            buttonStartRecording.setText("提交");
                            Toast.makeText(VideoRecorderActivity.this, "网络已恢复，可点击‘提交’返回", Toast.LENGTH_LONG).show();
                        });
                    }
                }
            };
            cm.registerDefaultNetworkCallback(connectivityCallback);
        } catch (Exception e) {
            Log.e(TAG, "registerConnectivityCallback failed", e);
        }
    }

    /**
     * 重写 onBackPressed 方法，防止用户在有待提交视频或正在录制时退出
     */
    @Override
    public void onBackPressed() {
        // 如果有待提交的视频，阻止用户退出
        if (pendingReturn) {
            Toast.makeText(this, "有待提交的视频，请提交后再返回", Toast.LENGTH_LONG).show();
            // 显示视频预览
            if (pendingReturnPath != null) {
                showVideoPreview(pendingReturnPath);
                videoControlPanel.setVisibility(View.VISIBLE);
            }
            return;
        }

        // 如果正在录制，提示用户需要先停止录制
        if (isRecording) {
            Toast.makeText(this, "请先停止当前录制", Toast.LENGTH_SHORT).show();
            return;
        }

        super.onBackPressed();
    }

    private void unregisterConnectivityCallback() {
        if (Build.VERSION.SDK_INT < 24) return;
        try {
            ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            if (cm != null && connectivityCallback != null) {
                cm.unregisterNetworkCallback(connectivityCallback);
                connectivityCallback = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "unregisterConnectivityCallback failed", e);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent 被调用");

        // 如果有待提交的视频，不处理新的deeplink
        if (pendingReturn) {
            Toast.makeText(this, "有待提交的视频，请先提交再执行其他操作", Toast.LENGTH_LONG).show();
            // 显示视频预览
            if (pendingReturnPath != null) {
                showVideoPreview(pendingReturnPath);
                videoControlPanel.setVisibility(View.VISIBLE);
                surfaceView.setVisibility(View.GONE);
                videoView.setVisibility(View.VISIBLE);
            }
            return;
        }

        setIntent(intent);

        // 从新的 Intent 获取 userId 和 taskId
        String newUserId = intent.getStringExtra("userId");
        long newTaskId = intent.getLongExtra("taskId", -1L);

        // 更新当前的 userId 和 taskId（如果提供了新的值）
        if (newUserId != null) {
            this.userId = newUserId;
        }
        if (newTaskId != -1L) {
            this.taskId = newTaskId;
        }

        Log.d(TAG, "onNewIntent: userId=" + userId + ", taskId=" + taskId);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d(TAG, "onActivityResult: requestCode=" + requestCode + ", resultCode=" + resultCode);

        // 移除可能导致问题的冗余代码
        if (requestCode == REQUEST_CODE_RECORD_VIDEO && resultCode == RESULT_OK && data != null) {
            String videoFilePath = data.getStringExtra("videoFilePath");
            String resultUserId = data.getStringExtra("userId");
            long resultTaskId = data.getLongExtra("taskId", -1L);

            if (videoFilePath != null) {
                // 创建视频对象并尝试提交
                Video newVideo = new Video(videoFilePath, "待上传", resultTaskId);
                newVideo.setUserId(resultUserId);

                // 直接提交视频到服务器
                submitVideoToServer(newVideo);
            } else {
                Log.w(TAG, "onActivityResult: videoFilePath 为 null");
                Toast.makeText(this, "视频文件路径为空", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 直接在VideoRecorderActivity中提交视频到服务器
     * @param video 视频对象
     */
    private void submitVideoToServer(Video video) {
        // 创建Gson实例，用于处理日期格式
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(Config.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        ApiService apiService = retrofit.create(ApiService.class);

        Log.d(TAG, "开始提交视频到服务器: filePath=" + video.getFilePath());

        apiService.createVideo(video).enqueue(new Callback<Video>() {
            @Override
            public void onResponse(Call<Video> call, Response<Video> response) {
                // 隐藏加载遮罩层
                showLoadingOverlay(false, null);

                // 恢复按钮状态
                buttonStartRecording.setEnabled(true);
                buttonPauseRecording.setEnabled(true);
                buttonSwitchCamera.setEnabled(true);

                if (response.isSuccessful()) {
                    Log.d(TAG, "视频提交成功");
                    Toast.makeText(VideoRecorderActivity.this, "视频提交成功", Toast.LENGTH_SHORT).show();

                    // 提交成功后，清理录制会话和待提交状态并返回结果
                    clearRecordingSession();
                    pendingReturn = false;
                    pendingReturnPath = null;

                    Intent resultIntent = new Intent();
                    resultIntent.putExtra("videoFilePath", video.getFilePath());
                    resultIntent.putExtra("userId", userId);
                    resultIntent.putExtra("taskId", taskId);
                    setResult(RESULT_OK, resultIntent);
                    finish();
                } else {
                    Log.e(TAG, "视频提交失败，HTTP 状态码: " + response.code() + ", 错误: " + response.message());
                    handleSubmissionFailure(video);
                }
            }

            @Override
            public void onFailure(Call<Video> call, Throwable t) {
                Log.e(TAG, "视频提交失败，异常: " + t.getMessage(), t);

                // 隐藏加载遮罩层
                showLoadingOverlay(false, null);

                // 恢复按钮状态
                buttonStartRecording.setEnabled(true);
                buttonPauseRecording.setEnabled(true);
                buttonSwitchCamera.setEnabled(true);

                handleSubmissionFailure(video);
            }
        });
    }

    /**
     * 处理视频提交失败的情况
     * @param video 视频对象
     */
    private void handleSubmissionFailure(Video video) {
        // 隐藏加载遮罩层
        showLoadingOverlay(false, null);

        Toast.makeText(this, "视频提交失败，请检查网络连接后重试", Toast.LENGTH_LONG).show();

        // 保存视频信息到本地，供后续重试
        pendingReturn = true;
        pendingReturnPath = video.getFilePath();
        buttonStartRecording.setText("提交");
        buttonStartRecording.setEnabled(true);

        Toast.makeText(this, "视频已保存，点击'提交'按钮可重新提交", Toast.LENGTH_LONG).show();
        Log.d(TAG, "视频提交失败，已保存视频文件: " + video.getFilePath());
    }

    protected void onPause() {
        super.onPause();
        if (isRecording) {
            // 后台/来电等情况下：暂停并持久化会话，回来继续
            if (!isPaused) {
                pauseRecording();
                Log.d(TAG, "onPause: 录制已暂停");
            }
            persistRecordingSession();
        }
        unregisterConnectivityCallback();
        Log.d(TAG, "onPause 被调用");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume 被调用");
        
        // 如果有待提交的视频，显示视频预览
        if (pendingReturn && pendingReturnPath != null) {
            showVideoPreview(pendingReturnPath);
            // 确保视频控制面板可见
            videoControlPanel.setVisibility(View.VISIBLE);
            // 确保SurfaceView隐藏，VideoView显示
            surfaceView.setVisibility(View.GONE);
            videoView.setVisibility(View.VISIBLE);
            
            // 如果正在录制但相机未初始化，重新初始化
            if (isRecording && camera == null) {
                Log.d(TAG, "恢复录制状态，重新初始化相机...");
                initializeCamera(currentCameraId);
            }
            
            Log.d(TAG, "显示待提交视频预览");
        } else if (isRecording && isPaused) {
            // 如果从暂停状态恢复且有录制会话，确保相机已初始化
            if (camera == null) {
                Log.d(TAG, "相机未初始化，正在重新初始化...");
                Toast.makeText(this, "正在重新初始化相机...", Toast.LENGTH_SHORT).show();
                initializeCamera(currentCameraId);
            }

            // 从暂停状态恢复时，询问用户是否继续录制
            handler.postDelayed(() -> {
                if (isRecording && isPaused) {
                    Toast.makeText(this, "点击继续按钮恢复录制", Toast.LENGTH_LONG).show();
                }
            }, 500);
        } else {
            // 没有待提交的视频时，确保摄像头预览可见
            surfaceView.setVisibility(View.VISIBLE);
            videoView.setVisibility(View.GONE);
            videoControlPanel.setVisibility(View.GONE);
            
            // 如果正在录制但相机未初始化，重新初始化
            if (isRecording && camera == null) {
                Log.d(TAG, "恢复录制状态，重新初始化相机...");
                initializeCamera(currentCameraId);
            }
        }
        registerConnectivityCallback();
        Log.d(TAG, "onResume 完成");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (isRecording) {
            // 不结束会话，只暂停并持久化，用户回来可继续
            if (!isPaused) {
                try { pauseRecording(); } catch (Exception ignore) {}
            }
            persistRecordingSession();
            Log.d(TAG, "onDestroy: 录制会话已保存，稍后可继续");
        }
        if (camera != null) {
            camera.release();
            camera = null;
            Log.d(TAG, "onDestroy 释放 Camera");
        }
        unregisterBatteryReceiver();
        handler.removeCallbacks(updateRecordingTimeRunnable);
        videoHandler.removeCallbacks(updateVideoProgressRunnable);
        Log.d(TAG, "onDestroy 被调用");
    }

    /**
     * 显示或隐藏加载遮罩层
     * @param show 是否显示
     * @param message 显示的消息
     */
    private void showLoadingOverlay(boolean show, String message) {
        if (loadingOverlay != null) {
            loadingOverlay.setVisibility(show ? View.VISIBLE : View.GONE);
            TextView loadingText = loadingOverlay.findViewById(R.id.loading_text);
            if (loadingText != null && message != null) {
                loadingText.setText(message);
            }
        }
    }

    /**
     * 显示视频预览
     * @param videoPath 视频路径
     */
    private void showVideoPreview(String videoPath) {
        if (videoView != null && videoPath != null) {
            // 隐藏摄像头预览，显示视频预览
            surfaceView.setVisibility(View.GONE);
            videoView.setVisibility(View.VISIBLE);
            videoControlPanel.setVisibility(View.VISIBLE);

            // 设置视频路径
            videoView.setVideoURI(Uri.fromFile(new File(videoPath)));

            // 准备视频
            videoView.setOnPreparedListener(mp -> {
                // 设置进度条最大值
                videoSeekbar.setMax(100);
                // 默认暂停状态
                buttonPlayPause.setText("播放");
                isVideoPlaying = false;
            });

            // 异步准备视频
            videoView.requestFocus();
            videoView.start();  // 开始准备
            videoView.pause();  // 立即暂停，等待用户操作
        }
    }

    /**
     * 隐藏视频预览，恢复摄像头预览
     */
    private void hideVideoPreview() {
        if (videoView != null) {
            // 停止视频播放
            if (isVideoPlaying) {
                videoView.stopPlayback();
                isVideoPlaying = false;
                videoHandler.removeCallbacks(updateVideoProgressRunnable);
            }

            // 隐藏视频预览，显示摄像头预览
            videoView.setVisibility(View.GONE);
            videoControlPanel.setVisibility(View.GONE);
            surfaceView.setVisibility(View.VISIBLE);
        }
    }

    private class BatteryReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
            int scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
            if (level != -1 && scale != -1) {
                float batteryPercentage = (level / (float) scale) * 100;
                Log.d(TAG, "电量广播: " + batteryPercentage + "%");

                if (batteryPercentage < 20 && isRecording) { // 修改为20%
                    Toast.makeText(VideoRecorderActivity.this, "电量低于20%，已自动暂停并保存会话，可稍后继续", Toast.LENGTH_LONG).show();
                    // 不结束录制会话，仅暂停并持久化，用户回来后可继续
                    if (!isPaused) {
                        pauseRecording();
                    }
                    persistRecordingSession();
                }
            }
        }
    }
}