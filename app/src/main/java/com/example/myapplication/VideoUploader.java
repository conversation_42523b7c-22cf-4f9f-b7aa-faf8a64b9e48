package com.example.myapplication;

import android.content.Context;
import android.os.AsyncTask;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

public class VideoUploader extends AsyncTask<Void, Integer, Boolean> {

    private Context context;
    private Video video;
    private VideoDAO videoDAO;
    private ProgressBar progressBar;
    private TextView statusTextView;

    public VideoUploader(Context context, Video video, ProgressBar progressBar, TextView statusTextView) {
        this.context = context;
        this.video = video;
        this.progressBar = progressBar;
        this.statusTextView = statusTextView;
        videoDAO = new VideoDAO(context);
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        videoDAO.open();
        statusTextView.setText("上传中");
        progressBar.setVisibility(View.VISIBLE);
        // 添加上传任务到 UploadManager
        // 添加上传任务
        // UploadManager.getInstance(context).addUploadTask(video.getFilePath(), video.getTaskId(), this);
    }


    @Override
    protected Boolean doInBackground(Void... voids) {
        // 模拟视频上传过程
        for (int i = 0; i <= 100; i++) {
            try {
                Thread.sleep(50); // 模拟上传过程
                publishProgress(i); // 更新进度
            } catch (InterruptedException e) {
                e.printStackTrace();
                return false;
            }
        }
        // 假设上传成功
        return true;
    }

    @Override
    protected void onProgressUpdate(Integer... values) {
        super.onProgressUpdate(values);
        progressBar.setProgress(values[0]);
    }

    @Override
    protected void onPostExecute(Boolean result) {
        super.onPostExecute(result);
        progressBar.setVisibility(View.GONE);
        if (result) {
            statusTextView.setText("已上传");
            video.setStatus("已上传");
            videoDAO.updateVideoStatus(video); // 更新数据库中的视频状态
        } else {
            statusTextView.setText("上传失败");
            video.setStatus("未上传");
            videoDAO.updateVideoStatus(video); // 更新数据库中的视频状态
        }
    }
}