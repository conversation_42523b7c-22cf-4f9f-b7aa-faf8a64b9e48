<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tabPendingUpload"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="待上传"
            android:gravity="center"
            android:padding="10dp"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tabUploaded"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="已上传"
            android:gravity="center"
            android:padding="10dp"
            android:textSize="16sp" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBarLoading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/batchControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="0dp"
            android:visibility="gone">

            <Button
                android:id="@+id/buttonSelectAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="全选" />

            <Button
                android:id="@+id/buttonBatchUpload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="批量上传" />

            <Button
                android:id="@+id/buttonBatchDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="批量删除" />

            <Button
                android:id="@+id/buttonCancelSelection"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消" />
        </LinearLayout>
    </HorizontalScrollView>

    <!-- 视频列表 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_videos"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        <TextView
            android:id="@+id/text_view_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="暂无视频"
            android:textSize="16sp"
            android:visibility="gone"/>
    </FrameLayout>

    <Button
        android:id="@+id/button_record_new_video"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="录制新视频" />

    <Button
        android:id="@+id/buttonToggleBatchMode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="进入批量操作模式" />
</LinearLayout>