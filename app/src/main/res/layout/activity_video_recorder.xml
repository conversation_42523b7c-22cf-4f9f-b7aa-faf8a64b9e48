<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

    <!-- SurfaceView 用于摄像头预览 -->
    <SurfaceView
        android:id="@+id/surface_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible" />

    <!-- VideoView 用于视频预览 -->
    <VideoView
        android:id="@+id/video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <!-- 加载遮罩层 -->
    <RelativeLayout
        android:id="@+id/loading_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:indeterminate="true" />

        <TextView
            android:id="@+id/loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/progress_bar"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="16dp"
            android:text="正在提交视频，请稍候..."
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </RelativeLayout>

    <!-- 视频控制面板 -->
    <LinearLayout
        android:id="@+id/video_control_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        android:background="#80000000">

        <!-- 进度条 -->
        <SeekBar
            android:id="@+id/video_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

        <!-- 控制按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/button_play_pause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="播放"
                android:layout_marginEnd="16dp" />

            <Button
                android:id="@+id/button_restart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重新开始" />
        </LinearLayout>
    </LinearLayout>

    <!-- 录制时间显示 -->
    <TextView
        android:id="@+id/text_view_recording_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:text="00:00:00"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:background="#80000000"
        android:padding="8dp" />

    <!-- 控制按钮布局 -->
    <LinearLayout
        android:id="@+id/control_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center">

        <Button
            android:id="@+id/button_switch_camera"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换摄像头"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/button_start_recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开始录制"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/button_pause_recording"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂停"
            android:visibility="gone" />
    </LinearLayout>

</RelativeLayout>