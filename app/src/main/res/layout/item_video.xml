<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/card_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题行，添加复选框 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <CheckBox
                android:id="@+id/checkBoxSelect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:textStyle="bold"
                android:ellipsize="end"
                android:maxLines="1"/>

            <TextView
                android:id="@+id/textViewStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:background="@drawable/status_bg"
                android:textColor="@color/status_text"/>

            <TextView
                android:id="@+id/textViewFlags"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/holo_red_dark" />
        </LinearLayout>

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="12dp"
            android:progressTint="@color/colorPrimary"
            android:progressBackgroundTint="@color/progress_bg"
            android:visibility="gone"/>

        <!-- 功能按钮组 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="end"
            android:divider="@drawable/divider_vertical"
            android:showDividers="middle">

            <Button
                android:id="@+id/buttonContinue"
                style="@style/ActionButton"
                android:text="继续"
                android:visibility="gone"
                android:drawableStart="@drawable/ic_continue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonPause"
                style="@style/ActionButton"
                android:text="暂停"
                android:visibility="gone"
                android:drawableStart="@drawable/ic_pause"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonUpload"
                style="@style/ActionButton.Primary"
                android:text="上传"
                android:drawableStart="@drawable/ic_upload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonCancel"
                style="@style/ActionButton.Warning"
                android:text="作废"
                android:drawableStart="@drawable/ic_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonDelete"
                style="@style/ActionButton.Danger"
                android:text="删除"
                android:drawableStart="@drawable/ic_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonLook"
                style="@style/ActionButton"
                android:text="查看"
                android:drawableStart="@drawable/ic_preview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>

            <Button
                android:id="@+id/buttonOnLineLook"
                style="@style/ActionButton"
                android:text="在线地址"
                android:drawableStart="@drawable/ic_preview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawablePadding="4dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>