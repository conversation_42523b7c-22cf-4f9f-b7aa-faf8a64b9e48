package com.example.myapplication;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

public class UploadProgressReceiver extends BroadcastReceiver {
    private VideoAdapter videoAdapter;
    private VideoDAO videoDAO;

    public UploadProgressReceiver(VideoAdapter videoAdapter, VideoDAO videoDAO) {
        this.videoAdapter = videoAdapter;
        this.videoDAO = videoDAO;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String videoPath = intent.getStringExtra("videoPath");
        String videoStatus = intent.getStringExtra("videoStatus");
        int taskId = intent.getIntExtra("taskId", -1);
        Log.d("UploadProgress", "Received: " + intent.getAction() + ", Path: " + videoPath + ", TaskId: " + taskId);

        if ("com.example.myapplication.UPLOAD_PROGRESS".equals(intent.getAction())) {
            int progress = intent.getIntExtra("progress", 0);
            Log.d("UploadProgress", "Progress: " + progress);
            videoAdapter.updateVideoProgress(videoPath, taskId, progress,videoStatus);
        } else if ("com.example.myapplication.UPLOAD_COMPLETE".equals(intent.getAction())) {
            Log.d("UploadProgress", "Upload complete");
            videoAdapter.updateVideoStatus(videoPath, taskId, "已上传");
        }
    }
}