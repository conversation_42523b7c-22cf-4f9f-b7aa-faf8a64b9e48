package com.example.myapplication;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class UploadService extends Service {
    private static final String TAG = "UploadService";
    private static final String CHANNEL_ID = "UploadServiceChannel";
    private static final int NOTIFICATION_ID = 1;
    private VideoDAO videoDAO;

    @Override
    public void onCreate() {
        super.onCreate();
        videoDAO = new VideoDAO(this);
        videoDAO.open();
        Log.d(TAG, "Service created");

        // 创建通知渠道（Android 8.0+ 需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "Upload Service",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String videoPath = intent.getStringExtra("videoPath");
            String videoStatus = intent.getStringExtra("videoStatus");
            int taskId = intent.getIntExtra("taskId", -1);
            Log.d(TAG, "Service started with videoPath: " + videoPath + ", taskId: " + taskId);

            // 启动前台服务
            Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle("视频上传")
                    .setContentText("正在上传视频: " + videoPath)
                    .setSmallIcon(android.R.drawable.ic_menu_upload)
                    .setOngoing(true)
                    .build();
            startForeground(NOTIFICATION_ID, notification);

            if (videoPath != null && taskId != -1) {
                new Thread(() -> uploadVideo(videoPath, taskId,videoStatus)).start();
            } else {
                Log.w(TAG, "Invalid intent data: videoPath or taskId missing");
                stopForeground(true);
                stopSelf();
            }
        } else {
            Log.w(TAG, "Service started with null intent");
            stopForeground(true);
            stopSelf();
        }
        return START_STICKY;
    }

    private void uploadVideo(String videoPath, int taskId,String videoStatus) {
        Log.d(TAG, "Starting upload for videoPath: " + videoPath + ", taskId: " + taskId);
        for (int i = 0; i <= 100; i += 10) { // 减少循环次数，便于调试
            try {
                Thread.sleep(500); // 增加间隔，便于观察日志
                Intent progressIntent = new Intent("com.example.myapplication.UPLOAD_PROGRESS");
                progressIntent.putExtra("progress", i);
                progressIntent.putExtra("videoPath", videoPath);
                progressIntent.putExtra("taskId", taskId);
                progressIntent.putExtra("videoStatus", videoStatus);
                LocalBroadcastManager.getInstance(this).sendBroadcast(progressIntent);
                Log.d(TAG, "Sent UPLOAD_PROGRESS broadcast - Progress: " + i + ", videoPath: " + videoPath + ", taskId: " + taskId);

                // 更新通知（可选）
                updateNotification(videoPath, i);
            } catch (InterruptedException e) {
                Log.e(TAG, "Upload interrupted", e);
            }
        }
        Intent completeIntent = new Intent("com.example.myapplication.UPLOAD_COMPLETE");
        completeIntent.putExtra("videoPath", videoPath);
        completeIntent.putExtra("taskId", taskId);
        LocalBroadcastManager.getInstance(this).sendBroadcast(completeIntent);
        Log.d(TAG, "Sent UPLOAD_COMPLETE broadcast - videoPath: " + videoPath + ", taskId: " + taskId);

        // 停止前台服务
        stopForeground(true);
        stopSelf();
    }

    private void updateNotification(String videoPath, int progress) {
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("视频上传")
                .setContentText("正在上传视频: "  + " (" + progress + "%)")
                .setSmallIcon(android.R.drawable.ic_menu_upload)
                .setOngoing(true)
                .build();
        NotificationManager manager = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        }
        manager.notify(NOTIFICATION_ID, notification);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        videoDAO.close();
        Log.d(TAG, "Service destroyed");
    }
}