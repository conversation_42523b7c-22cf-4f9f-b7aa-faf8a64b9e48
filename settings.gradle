pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        // Extra mirrors/fallbacks to improve availability in CN/network-restricted environments
        maven { url "https://s01.oss.sonatype.org/content/repositories/releases/" }
        maven { url "https://repo1.maven.org/maven2" }
        maven { url "https://maven-central.storage-download.googleapis.com/maven2/" }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url "https://maven.aliyun.com/repository/central" }
        maven { url "https://mirrors.cloud.tencent.com/nexus/repository/maven-public/" }
    }
}

rootProject.name = "My Application"
include ':app'
